import Foundation

struct Channel: Decodable {
    let channelId: String?
    let channelName: String
    let channelImageUrl: String?
    let verifiedMark: Bool?
    let channelDescription: String?
    let followerCount: Int?
    let openLive: Bool?
    let personalData: PersonalData?
}

struct PersonalData: Decodable {
    let following: FollowingData?
}

struct FollowingData: Decodable {
    let following: Bool
    let followDate: String?
}

extension Channel {
    func toUIChannel() -> UIChannel {
        var imageUrl: URL?
        if let urlString = self.channelImageUrl {
            imageUrl = URL(string: urlString)
        }
        
        return UIChannel(
            id: self.channelId,
            name: self.channelName,
            imageUrl: imageUrl,
            description: channelDescription,
            isVerified: self.verifiedMark,
            followerCount: self.followerCount,
            isLive: self.openLive,
            isFollowing: self.personalData?.following?.following
        )
    }
}

struct SearchChannelResponse: Decodable {
    let code: Int
    let message: String
    let content: SearchChannelContent
}

struct SearchChannelContent: Decodable {
    let channels: [Channel]
    let size: Int
    let offset: Int
    let total: Int
} 

struct ChannelResponse: Decodable {
    let code: Int
    let message: String?
    let content: Channel
}

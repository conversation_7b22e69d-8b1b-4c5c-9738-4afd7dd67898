import Foundation

enum APIEndpoint {
    // Base URLs
    static let baseURL = "https://api.chzzk.naver.com"
    static let serviceURL = "\(baseURL)/service"
    static let naverGameURL = "https://comm-api.game.naver.com/nng_main"
    
    // Auth
    static let userStatus = "\(naverGameURL)/v1/user/getUserStatus"
    static let naverLogin = "https://nid.naver.com/nidlogin.login"
    
    // Live
    static let allLives = "\(serviceURL)/v1/lives"
    static let liveDetail = "\(serviceURL)/v3/channels"
    static let liveStatus = "\(baseURL)/polling/v3/channels"
    static let followingLives = "\(serviceURL)/v1/channels/following-lives"
    
    // Categories
    static let categories = "\(serviceURL)/v1/categories/live"
    static let followingCategories = "\(serviceURL)/v1/categories/following"
    static let categoryLives = "\(serviceURL)/v2/categories"
    
    // Search
    static let searchChannels = "\(serviceURL)/v1/search/channels"
    static let searchChannelAutocomplete = "\(serviceURL)/v1/search/channels/auto-complete"
    static let searchLoungesAutocomplete = "\(naverGameURL)/v2/search/lounges/auto-complete"
    static let searchTags = "\(serviceURL)/v1/tag/lives"
    static let searchLives = "\(serviceURL)/v1/search/lives"
    static let searchVideos = "\(serviceURL)/v1/search/videos"
    
    // VOD
    static let popularVods = "\(serviceURL)/v1/home/<USER>"
    static let vodPlayback = "https://apis.naver.com/neonplayer/vodplay/v1/playback"
    static let vodPlaybackV2 = "https://apis.naver.com/neonplayer/vodplay/v2/playback"
    static let followingVods = "\(serviceURL)/v1/my-content"
    static let vodDetail = "\(serviceURL)/v3/videos" // For getting video details
    static let followingVideos = "\(serviceURL)/v2/home/<USER>/videos"
    static let videoWatchEvent = "\(baseURL)/polling/v1/watch-event/video"
    
    // Channel
    static let channel = "\(serviceURL)/v1/channels"
    static let channelVideos = "\(serviceURL)/v1/channels" // /channelId/videos
    static let channelData = "\(serviceURL)/v1/channels" // /channelId/data
    static let followingChannels = "\(serviceURL)/v1/channels/followings"
    
    // Home
    static let recommendChannels = "\(serviceURL)/v1/home/<USER>"
}

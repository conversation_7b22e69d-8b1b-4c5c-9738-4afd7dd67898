//
//  VideoDownloadService.swift
//  ChzzkTV
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 8/27/25.
//

import Foundation
import AVFoundation
import os.log
import UIKit

enum VideoDownloadError: Error, LocalizedError {
    case invalidURL
    case downloadFailed(Error)
    case conversionFailed
    case insufficientStorage
    case networkUnavailable
    case cancelled
    
    var errorDescription: String? {
        switch self {
        case .invalidURL:
            return "Invalid video URL"
        case .downloadFailed(let error):
            return "Download failed: \(error.localizedDescription)"
        case .conversionFailed:
            return "Video conversion failed"
        case .insufficientStorage:
            return "Insufficient storage space"
        case .networkUnavailable:
            return "Network unavailable"
        case .cancelled:
            return "Download cancelled"
        }
    }
}

enum VideoDownloadState: Codable {
    case pending
    case downloading(progress: Double)
    case processing
    case completed(URL)
    case failed(String) // Store error message as string instead of Error
    case cancelled

    enum CodingKeys: String, CodingKey {
        case type, progress, url, errorMessage
    }

    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: Coding<PERSON><PERSON>s.self)
        let type = try container.decode(String.self, forKey: .type)

        switch type {
        case "pending":
            self = .pending
        case "downloading":
            let progress = try container.decode(Double.self, forKey: .progress)
            self = .downloading(progress: progress)
        case "processing":
            self = .processing
        case "completed":
            let url = try container.decode(URL.self, forKey: .url)
            self = .completed(url)
        case "failed":
            let errorMessage = try container.decode(String.self, forKey: .errorMessage)
            self = .failed(errorMessage)
        case "cancelled":
            self = .cancelled
        default:
            throw DecodingError.dataCorrupted(DecodingError.Context(codingPath: decoder.codingPath, debugDescription: "Unknown VideoDownloadState type: \(type)"))
        }
    }

    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)

        switch self {
        case .pending:
            try container.encode("pending", forKey: .type)
        case .downloading(let progress):
            try container.encode("downloading", forKey: .type)
            try container.encode(progress, forKey: .progress)
        case .processing:
            try container.encode("processing", forKey: .type)
        case .completed(let url):
            try container.encode("completed", forKey: .type)
            try container.encode(url, forKey: .url)
        case .failed(let errorMessage):
            try container.encode("failed", forKey: .type)
            try container.encode(errorMessage, forKey: .errorMessage)
        case .cancelled:
            try container.encode("cancelled", forKey: .type)
        }
    }
}

struct VideoDownloadInfo: Identifiable {
    let video: UIVideo
    let quality: VideoQuality
    let downloadURL: URL
    let destinationURL: URL
    var state: VideoDownloadState
    var startTime: Date
    var totalBytes: Int64
    var downloadedBytes: Int64

    var id: Int { video.id }

    var progress: Double {
        guard totalBytes > 0 else { return 0.0 }
        return Double(downloadedBytes) / Double(totalBytes)
    }
}

@MainActor
class VideoDownloadService: NSObject, ObservableObject, @preconcurrency VideoDownloadServiceProtocol {
    static let shared = VideoDownloadService()
    
    // MARK: - Published Properties
    @Published var activeDownloads: [Int: VideoDownloadInfo] = [:]
    @Published var completedDownloads: [Int: VideoDownloadInfo] = [:]
    
    // MARK: - Private Properties
    private let logger = Logger(subsystem: "ChzzkTV", category: "VideoDownload")
    private let fileManager = FileManager.default
    private let videoService: VideoServiceProtocol
    private let downloadSession: URLSession
    private let urlExtractor: VideoDownloadURLExtractor
    private var downloadTasks: [Int: URLSessionDownloadTask] = [:]


    // Track active download tasks for cancellation
    private var activeTasks: [Int: Task<URL, Error>] = [:]

    // Download directory
    private let downloadsDirectory: URL
    
    // MARK: - Initialization
    init(videoService: VideoServiceProtocol = VideoService()) {
        logger.info("Initializing VideoDownloadService...")
        self.videoService = videoService
        self.urlExtractor = VideoDownloadURLExtractor()

        // Create downloads directory that's accessible via Files app
        let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!
        self.downloadsDirectory = documentsPath

        // Configure download session
        let config = URLSessionConfiguration.background(withIdentifier: "com.chzzkTV.videoDownload")
        config.isDiscretionary = false
        config.sessionSendsLaunchEvents = true
        self.downloadSession = URLSession(configuration: config, delegate: nil, delegateQueue: nil)

        super.init()

        createDownloadsDirectoryIfNeeded()
        loadCompletedDownloads()
        logger.info("VideoDownloadService initialization complete")
    }
    
    // MARK: - Public Methods

    /// Opens the Files app and navigates to the app's downloads folder
    @MainActor
    func openFilesApp() {
        // Create the Files app URL scheme to open the app's Documents directory
        // The shareddocuments:// scheme opens Files app to the app's shared documents
        if let url = URL(string: "shareddocuments://") {
            if UIApplication.shared.canOpenURL(url) {
                UIApplication.shared.open(url)
                logger.info("Opened Files app to app documents folder")
            } else {
                // Fallback: try to open Files app directly
                if let filesURL = URL(string: "files://") {
                    UIApplication.shared.open(filesURL)
                    logger.info("Opened Files app (fallback)")
                }
            }
        }
    }

    /// Gets the file URL for a downloaded video to use with ShareLink
    func getDownloadedFileURL(for download: VideoDownloadInfo) -> URL? {
        guard let fileURL = findActualDownloadFile(for: download) else {
            logger.error("Could not find downloaded file for: \(download.video.title)")
            return nil
        }

        return fileURL
    }

    /// Updates the file size for a completed download
    func updateFileSizeForCompletedDownload(videoId: Int) {
        guard var download = completedDownloads[videoId] else { return }

        // Only update if we don't already have the file size
        guard download.totalBytes == 0 else { return }

        if let fileURL = findActualDownloadFile(for: download) {
            do {
                let attributes = try fileManager.attributesOfItem(atPath: fileURL.path)
                if let fileSize = attributes[.size] as? Int64 {
                    download.totalBytes = fileSize
                    download.downloadedBytes = fileSize
                    completedDownloads[videoId] = download
                    saveCompletedDownloads()
                    logger.info("Updated file size for \(download.video.title): \(fileSize) bytes")
                }
            } catch {
                logger.error("Failed to get file size for \(download.video.title): \(error.localizedDescription)")
            }
        }
    }
    
    func downloadVideo(_ video: UIVideo, quality: VideoQuality) async throws -> URL {
        logger.info("Starting download for video: \(video.title) at \(quality.displayName)")
        
        // Check if already downloading or completed
        if activeDownloads[video.id] != nil {
            throw VideoDownloadError.downloadFailed(NSError(domain: "VideoDownload", code: -1, userInfo: [NSLocalizedDescriptionKey: "Video is already being downloaded"]))
        }
        
        if let completed = completedDownloads[video.id] {
            return completed.destinationURL
        }
        
        // Check available storage
        try checkAvailableStorage()
        
        // Get video details and download URL
        let downloadURL = try await getDownloadURL(for: video, quality: quality)
        
        // Create destination URL
        let destinationURL = createDestinationURL(for: video, quality: quality)
        
        // Create download info
        let downloadInfo = VideoDownloadInfo(
            video: video,
            quality: quality,
            downloadURL: downloadURL,
            destinationURL: destinationURL,
            state: .pending,
            startTime: Date(),
            totalBytes: 0,
            downloadedBytes: 0
        )
        
        activeDownloads[video.id] = downloadInfo
        
        // Create and store the download task
        let downloadTask = Task {
            do {
                let finalURL = try await performDownload(downloadInfo)

                // Move to completed downloads
                var completedInfo = downloadInfo
                completedInfo.state = .completed(finalURL)
                completedDownloads[video.id] = completedInfo
                activeDownloads.removeValue(forKey: video.id)
                activeTasks.removeValue(forKey: video.id)

                // Save completed downloads
                saveCompletedDownloads()

                logger.info("Successfully downloaded video: \(video.title)")
                return finalURL

            } catch is CancellationError {
                logger.info("Download cancelled for video: \(video.title)")

                // Clean up on cancellation
                activeDownloads.removeValue(forKey: video.id)
                activeTasks.removeValue(forKey: video.id)

                // Clean up any partial files
                try? fileManager.removeItem(at: downloadInfo.destinationURL)

                throw CancellationError()

            } catch {
                // Remove from active downloads on failure
                activeDownloads.removeValue(forKey: video.id)
                activeTasks.removeValue(forKey: video.id)
                logger.error("Failed to download video: \(error.localizedDescription)")
                throw VideoDownloadError.downloadFailed(error)
            }
        }

        // Store the task for cancellation
        activeTasks[video.id] = downloadTask

        // Wait for the task to complete (this will throw if cancelled)
        return try await downloadTask.value
    }
    
    func getDownloadProgress(for videoId: Int) -> VideoDownloadState? {
        if let activeDownload = activeDownloads[videoId] {
            return activeDownload.state
        }
        
        if let completedDownload = completedDownloads[videoId] {
            return completedDownload.state
        }
        
        return nil
    }

    func cancelDownload(for videoId: Int) {
        guard let downloadInfo = activeDownloads[videoId] else { return }

        // Cancel the URLSession download task
        downloadTasks[videoId]?.cancel()
        downloadTasks.removeValue(forKey: videoId)

        // Cancel the async Task for segment-based downloads
        activeTasks[videoId]?.cancel()
        activeTasks.removeValue(forKey: videoId)

        // Update state
        var cancelledInfo = downloadInfo
        cancelledInfo.state = .cancelled
        activeDownloads.removeValue(forKey: videoId)

        // Clean up any partial files
        try? fileManager.removeItem(at: downloadInfo.destinationURL)

        logger.info("Cancelled download for video ID: \(videoId)")
    }
    
    func getDownloadedVideos() -> [VideoDownloadInfo] {
        return Array(completedDownloads.values).sorted { $0.startTime > $1.startTime }
    }
    
    func deleteDownloadedVideo(for videoId: Int) throws {
        guard let downloadInfo = completedDownloads[videoId] else { return }

        // If the file doesn't exist at the expected location, try to find the actual file
        var fileToDelete = downloadInfo.destinationURL
        if !fileManager.fileExists(atPath: downloadInfo.destinationURL.path) {
            if let actualFile = findActualDownloadFile(for: downloadInfo) {
                fileToDelete = actualFile
            } else {
                // Still remove from completed downloads even if file is missing
                completedDownloads.removeValue(forKey: videoId)
                saveCompletedDownloads()
                return
            }
        }

        // Delete the file
        try fileManager.removeItem(at: fileToDelete)

        // Remove from completed downloads
        completedDownloads.removeValue(forKey: videoId)
        saveCompletedDownloads()

        logger.info("Deleted downloaded video for ID: \(videoId)")
    }
    
    // MARK: - Private Methods
    
    private func getDownloadURL(for video: UIVideo, quality: VideoQuality) async throws -> URL {
        logger.info("Getting download URL for video: \(video.title), quality: \(quality.displayName)")
        logger.debug("Original quality URL: \(quality.url.absoluteString)")

        // Use the URL extractor to get the proper download URL
        let downloadURL = try await urlExtractor.extractDownloadURL(from: quality)
        logger.info("Extracted download URL: \(downloadURL.absoluteString)")

        return downloadURL
    }
    
    private func performDownload(_ downloadInfo: VideoDownloadInfo) async throws -> URL {
        // Update state to downloading
        var updatedInfo = downloadInfo
        updatedInfo.state = .downloading(progress: 0.0)
        activeDownloads[downloadInfo.video.id] = updatedInfo

        // For HLS streams, we'll use a different approach
        if downloadInfo.downloadURL.absoluteString.contains(".m3u8") {
            return try await downloadHLSStream(downloadInfo)
        } else {
            // For direct video URLs, use regular download
            return try await downloadDirectVideo(downloadInfo)
        }
    }
    
    private func downloadHLSStream(_ downloadInfo: VideoDownloadInfo) async throws -> URL {
        logger.info("Downloading HLS stream for video: \(downloadInfo.video.title)")

        // Update state to processing
        var updatedInfo = downloadInfo
        updatedInfo.state = .processing
        await MainActor.run {
            activeDownloads[downloadInfo.video.id] = updatedInfo
        }

        // Skip all the checks and go straight to segment-based download (the only method that works)
        return try await downloadHLSSegments(from: downloadInfo.downloadURL, destinationURL: downloadInfo.destinationURL, videoId: downloadInfo.video.id)
    }

    private func downloadDirectVideo(_ downloadInfo: VideoDownloadInfo) async throws -> URL {
        logger.info("Downloading direct video for: \(downloadInfo.video.title)")

        // For direct video URLs, use URLSession to download
        let (data, response) = try await URLSession.shared.data(from: downloadInfo.downloadURL)

        guard let httpResponse = response as? HTTPURLResponse,
              200...299 ~= httpResponse.statusCode else {
            throw VideoDownloadError.downloadFailed(NSError(domain: "HTTPError", code: -1))
        }

        // Write data to destination
        try data.write(to: downloadInfo.destinationURL)
        return downloadInfo.destinationURL
    }



    private func downloadHLSSegments(from hlsURL: URL, destinationURL: URL, videoId: Int) async throws -> URL {
        logger.info("Starting segment-based HLS download from: \(hlsURL.absoluteString)")

        // Extract query parameters (GDA token) from the original URL
        guard let queryItems = extractQueryItems(from: hlsURL) else {
            logger.error("Failed to extract query items from HLS URL")
            throw VideoDownloadError.downloadFailed(NSError(
                domain: "VideoDownload",
                code: -1,
                userInfo: [NSLocalizedDescriptionKey: "Failed to extract authentication parameters from video URL"]
            ))
        }

        // Download and parse the M3U8 playlist
        let playlistContent = try await downloadPlaylistContent(from: hlsURL)
        let segmentURLs = try parseSegmentURLs(from: playlistContent, baseURL: hlsURL)

        logger.info("Found \(segmentURLs.count) segments to download")

        // Download all segments and concatenate them
        return try await downloadAndConcatenateSegments(
            segmentURLs: segmentURLs,
            queryItems: queryItems,
            destinationURL: destinationURL,
            videoId: videoId
        )
    }

    private func extractQueryItems(from url: URL) -> [URLQueryItem]? {
        guard let components = URLComponents(url: url, resolvingAgainstBaseURL: false),
              let queryItems = components.queryItems else {
            return nil
        }
        return queryItems
    }

    private func downloadPlaylistContent(from url: URL) async throws -> String {
        let (data, _) = try await URLSession.shared.data(from: url)
        guard let content = String(data: data, encoding: .utf8) else {
            throw VideoDownloadError.downloadFailed(NSError(
                domain: "VideoDownload",
                code: -1,
                userInfo: [NSLocalizedDescriptionKey: "Failed to decode playlist content"]
            ))
        }
        return content
    }

    private func parseSegmentURLs(from playlistContent: String, baseURL: URL) throws -> [URL] {
        let lines = playlistContent.components(separatedBy: .newlines)
        var segmentURLs: [URL] = []

        for line in lines {
            let trimmedLine = line.trimmingCharacters(in: .whitespacesAndNewlines)

            // Skip empty lines and comments (except #EXT-X-MAP which we might need)
            if trimmedLine.isEmpty || trimmedLine.hasPrefix("#") {
                continue
            }

            // This should be a segment URL
            if let segmentURL = URL(string: trimmedLine, relativeTo: baseURL)?.absoluteURL {
                segmentURLs.append(segmentURL)
            }
        }

        if segmentURLs.isEmpty {
            throw VideoDownloadError.downloadFailed(NSError(
                domain: "VideoDownload",
                code: -1,
                userInfo: [NSLocalizedDescriptionKey: "No video segments found in playlist"]
            ))
        }

        return segmentURLs
    }

    private func downloadAndConcatenateSegments(
        segmentURLs: [URL],
        queryItems: [URLQueryItem],
        destinationURL: URL,
        videoId: Int
    ) async throws -> URL {
        logger.info("Downloading \(segmentURLs.count) segments...")

        // Create a temporary directory for segments
        let tempDir = FileManager.default.temporaryDirectory.appendingPathComponent(UUID().uuidString)
        try FileManager.default.createDirectory(at: tempDir, withIntermediateDirectories: true)

        defer {
            // Clean up temp directory
            try? FileManager.default.removeItem(at: tempDir)
        }

        var segmentFiles: [URL] = []

        // Download segments with progress tracking
        for (index, segmentURL) in segmentURLs.enumerated() {
            // Check for cancellation
            try Task.checkCancellation()

            let segmentData = try await downloadSegmentWithAuth(segmentURL: segmentURL, queryItems: queryItems)
            let segmentFile = tempDir.appendingPathComponent("segment_\(String(format: "%05d", index)).ts")
            try segmentData.write(to: segmentFile)
            segmentFiles.append(segmentFile)

            // Update progress
            let progress = Float(index + 1) / Float(segmentURLs.count)

            // Update download progress in the main actor
            await MainActor.run {
                if var downloadInfo = activeDownloads[videoId] {
                    downloadInfo.state = .downloading(progress: Double(progress))
                    activeDownloads[videoId] = downloadInfo
                }
            }

            // Log progress every 10 segments
            if index % 10 == 0 || index == segmentURLs.count - 1 {
                logger.info("Progress: \(index + 1)/\(segmentURLs.count) segments downloaded")
            }
        }

        logger.info("All segments downloaded, concatenating...")

        // Concatenate all segments into final TS file (skip MP4 conversion since it always fails)
        let tsDestinationURL = destinationURL.deletingPathExtension().appendingPathExtension("ts")
        return try await concatenateSegmentsDirectly(segmentFiles: segmentFiles, destinationURL: tsDestinationURL)
    }

    private func downloadSegmentWithAuth(segmentURL: URL, queryItems: [URLQueryItem]) async throws -> Data {
        // Add authentication parameters to segment URL
        guard var components = URLComponents(url: segmentURL, resolvingAgainstBaseURL: false) else {
            logger.error("Invalid segment URL: \(segmentURL.absoluteString)")
            throw VideoDownloadError.downloadFailed(NSError(
                domain: "VideoDownload",
                code: -1,
                userInfo: [NSLocalizedDescriptionKey: "Invalid segment URL: \(segmentURL.absoluteString)"]
            ))
        }

        components.queryItems = queryItems

        guard let authenticatedURL = components.url else {
            logger.error("Failed to create authenticated segment URL")
            throw VideoDownloadError.downloadFailed(NSError(
                domain: "VideoDownload",
                code: -1,
                userInfo: [NSLocalizedDescriptionKey: "Failed to create authenticated segment URL"]
            ))
        }

        let (data, response) = try await URLSession.shared.data(from: authenticatedURL)

        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200 else {
            let statusCode = (response as? HTTPURLResponse)?.statusCode ?? -1
            logger.error("Failed to download segment: HTTP \(statusCode)")
            throw VideoDownloadError.downloadFailed(NSError(
                domain: "VideoDownload",
                code: -1,
                userInfo: [NSLocalizedDescriptionKey: "Failed to download segment: HTTP \(statusCode)"]
            ))
        }

        return data
    }

    private func concatenateSegmentsDirectly(segmentFiles: [URL], destinationURL: URL) async throws -> URL {
        logger.info("Concatenating \(segmentFiles.count) segments directly to TS file")

        // Remove destination file if it exists
        try? FileManager.default.removeItem(at: destinationURL)

        logger.info("Creating TS file...")
        let outputData = NSMutableData()

        for (index, segmentFile) in segmentFiles.enumerated() {
            let segmentData = try Data(contentsOf: segmentFile)
            outputData.append(segmentData)

            if index % 50 == 0 {
                logger.info("Concatenated segment \(index + 1)/\(segmentFiles.count)")
            }
        }

        // Write concatenated TS data directly to destination
        try outputData.write(to: destinationURL)
        logger.info("Created TS file: \(destinationURL.path)")

        return destinationURL
    }
    
    private func createDestinationURL(for video: UIVideo, quality: VideoQuality) -> URL {
        // Create filename with format: <channel.name>_<title>_YYYYMMDD_<quality>.ts
        let channelName = video.channel?.name ?? "Unknown"
        let date = Date(timeIntervalSince1970: video.timestamp)
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyyMMdd HHmmss"
        let dateString = dateFormatter.string(from: date)

        let filename = sanitizeFilename("\(channelName)_\(video.title)_\(dateString)_\(quality.displayName).ts")
        return downloadsDirectory.appendingPathComponent(filename)
    }
    
    private func sanitizeFilename(_ filename: String) -> String {
        let invalidCharacters = CharacterSet(charactersIn: ":/\\?%*|\"<>")
        return filename.components(separatedBy: invalidCharacters).joined(separator: "_")
    }
    
    private func checkAvailableStorage() throws {
        guard let attributes = try? fileManager.attributesOfFileSystem(forPath: downloadsDirectory.path),
              let freeSize = attributes[.systemFreeSize] as? NSNumber else {
            return // Can't determine free space, proceed anyway
        }
        
        let freeBytes = freeSize.int64Value
        let requiredBytes: Int64 = 500 * 1024 * 1024 // Require at least 500MB free
        
        if freeBytes < requiredBytes {
            throw VideoDownloadError.insufficientStorage
        }
    }
    
    nonisolated private func createDownloadsDirectoryIfNeeded() {
        if !FileManager.default.fileExists(atPath: downloadsDirectory.path) {
            do {
                try FileManager.default.createDirectory(at: downloadsDirectory, withIntermediateDirectories: true)
                try setupFilesAppAccess()
            } catch {
                logger.error("Failed to create downloads directory: \(error.localizedDescription)")
            }
        } else {
            // Ensure Files app access is still configured
            try? setupFilesAppAccess()
        }
    }

    nonisolated private func setupFilesAppAccess() throws {
        // Set resource values to make the directory visible in Files app
        var resourceValues = URLResourceValues()
        resourceValues.isExcludedFromBackup = false

        var mutableURL = downloadsDirectory
        try mutableURL.setResourceValues(resourceValues)
    }

    nonisolated private func migrateExistingDownloads() {
        // Check if old Downloads folder exists and migrate files
        let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!
        let oldDownloadsDirectory = documentsPath.appendingPathComponent("Downloads")

        guard FileManager.default.fileExists(atPath: oldDownloadsDirectory.path) else {
            print("🔥 MIGRATE: No old downloads directory found, skipping migration")
            return
        }

        print("🔥 MIGRATE: Found old downloads directory, migrating files...")

        do {
            let files = try FileManager.default.contentsOfDirectory(at: oldDownloadsDirectory, includingPropertiesForKeys: nil)

            for file in files {
                let destinationURL = downloadsDirectory.appendingPathComponent(file.lastPathComponent)

                // Only move if destination doesn't exist
                if !FileManager.default.fileExists(atPath: destinationURL.path) {
                    try FileManager.default.moveItem(at: file, to: destinationURL)
                    print("🔥 MIGRATE: ✅ Moved \(file.lastPathComponent) to new location")
                } else {
                    print("🔥 MIGRATE: ⚠️ File already exists at destination: \(file.lastPathComponent)")
                }
            }

            // Remove old directory if it's empty
            let remainingFiles = try FileManager.default.contentsOfDirectory(at: oldDownloadsDirectory, includingPropertiesForKeys: nil)
            if remainingFiles.isEmpty {
                try FileManager.default.removeItem(at: oldDownloadsDirectory)
                print("🔥 MIGRATE: ✅ Removed empty old downloads directory")
            }

            print("🔥 MIGRATE: Migration completed successfully")

        } catch {
            print("🔥 MIGRATE: ❌ Migration failed: \(error)")
        }
    }

    nonisolated private func saveCompletedDownloads() {
        Task { @MainActor in
            let encoder = JSONEncoder()
            let downloads = Array(completedDownloads.values)
            logger.info("Saving \(downloads.count) completed downloads to UserDefaults...")

            do {
                let data = try encoder.encode(downloads)
                UserDefaults.standard.set(data, forKey: "completedVideoDownloads")
                logger.info("Successfully saved completed downloads")
            } catch {
                logger.error("Failed to encode completed downloads: \(error.localizedDescription)")
            }
        }
    }

    nonisolated private func loadCompletedDownloads() {
        logger.info("Loading completed downloads from UserDefaults...")

        guard let data = UserDefaults.standard.data(forKey: "completedVideoDownloads") else {
            logger.info("No completed downloads data found in UserDefaults")
            return
        }

        do {
            let downloads = try JSONDecoder().decode([VideoDownloadInfo].self, from: data)
            logger.info("Successfully decoded \(downloads.count) completed downloads")

            // Verify files still exist and update completed downloads
            Task { @MainActor in
                var validDownloads = 0
                for download in downloads {
                    if FileManager.default.fileExists(atPath: download.destinationURL.path) {
                        completedDownloads[download.video.id] = download
                        validDownloads += 1
                    } else {
                        // Try to find the file with different extensions and paths
                        if let actualURL = findActualDownloadFile(for: download) {
                            // Create new download info with the correct path
                            let updatedDownload = VideoDownloadInfo(
                                video: download.video,
                                quality: download.quality,
                                downloadURL: download.downloadURL,
                                destinationURL: actualURL,
                                state: .completed(actualURL),
                                startTime: download.startTime,
                                totalBytes: download.totalBytes,
                                downloadedBytes: download.downloadedBytes
                            )
                            completedDownloads[download.video.id] = updatedDownload
                            validDownloads += 1
                        }
                    }
                }
                logger.info("Restored \(validDownloads) valid downloads out of \(downloads.count)")
            }
        } catch {
            logger.error("Failed to decode completed downloads: \(error.localizedDescription)")
        }
    }

    // MARK: - File Management Methods

    private func findActualDownloadFile(for download: VideoDownloadInfo) -> URL? {
        // Create the expected filename based on new format: <channel.name>_<title>_YYYYMMDD_<quality>
        let channelName = download.video.channel?.name ?? "Unknown"
        let date = Date(timeIntervalSince1970: download.video.timestamp)
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyyMMdd HHmmss"
        let dateString = dateFormatter.string(from: date)

        let baseFilename = sanitizeFilename("\(channelName)_\(download.video.title)_\(dateString)_\(download.quality.displayName)")

        // Try different extensions (.ts, .mp4)
        let possibleExtensions = ["ts", "mp4"]

        for ext in possibleExtensions {
            let filename = "\(baseFilename).\(ext)"
            let fileURL = downloadsDirectory.appendingPathComponent(filename)

            if FileManager.default.fileExists(atPath: fileURL.path) {
                return fileURL
            }
        }

        // Also try the original filename in case it was saved differently
        let originalFileName = download.destinationURL.lastPathComponent
        let originalPath = downloadsDirectory.appendingPathComponent(originalFileName)
        if FileManager.default.fileExists(atPath: originalPath.path) {
            return originalPath
        }

        return nil
    }

    // MARK: - Utility Methods

    private func withTimeout<T>(seconds: TimeInterval, operation: @escaping () async throws -> T) async throws -> T {
        return try await withThrowingTaskGroup(of: T.self) { group in
            group.addTask {
                try await operation()
            }

            group.addTask {
                try await Task.sleep(nanoseconds: UInt64(seconds * 1_000_000_000))
                throw VideoDownloadError.downloadFailed(NSError(domain: "Timeout", code: -1, userInfo: [NSLocalizedDescriptionKey: "Operation timed out after \(seconds) seconds"]))
            }

            let result = try await group.next()!
            group.cancelAll()
            return result
        }
    }
}

// MARK: - VideoDownloadInfo Codable Extension
extension VideoDownloadInfo: Codable {
    enum CodingKeys: String, CodingKey {
        case video, quality, downloadURL, destinationURL, state, startTime, totalBytes, downloadedBytes
    }

    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        video = try container.decode(UIVideo.self, forKey: .video)
        quality = try container.decode(VideoQuality.self, forKey: .quality)
        downloadURL = try container.decode(URL.self, forKey: .downloadURL)
        destinationURL = try container.decode(URL.self, forKey: .destinationURL)
        state = try container.decodeIfPresent(VideoDownloadState.self, forKey: .state) ?? .completed(destinationURL)
        startTime = try container.decode(Date.self, forKey: .startTime)
        totalBytes = try container.decode(Int64.self, forKey: .totalBytes)
        downloadedBytes = try container.decode(Int64.self, forKey: .downloadedBytes)
    }

    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(video, forKey: .video)
        try container.encode(quality, forKey: .quality)
        try container.encode(downloadURL, forKey: .downloadURL)
        try container.encode(destinationURL, forKey: .destinationURL)
        try container.encode(state, forKey: .state)
        try container.encode(startTime, forKey: .startTime)
        try container.encode(totalBytes, forKey: .totalBytes)
        try container.encode(downloadedBytes, forKey: .downloadedBytes)
    }
}


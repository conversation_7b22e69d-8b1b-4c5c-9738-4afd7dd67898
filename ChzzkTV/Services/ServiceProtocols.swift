import Foundation
import SwiftData
import AVKit

protocol ChannelServiceProtocol {
    func getChannel(id: String) async throws -> UIChannel
    func getLive(id: String) async throws -> UILiveStream?
    func getLiveWithPlaybackUrl(id: String) async throws -> UILiveStream?
    func getLatestVideo(id: String) async throws -> UIVideo?
    func getVideos(id: String, page: Int, size: Int) async throws -> ([UIVideo]?, Int?)
    func getFollowingChannels() async throws -> [UIChannel]
    func isFollowing(channelId: String) -> Bool
    func toggleFollow(channelId: String) async -> Bool
}

protocol VideoServiceProtocol {
    func getVideo(_ videoId: Int) async throws -> (UIVideo, Bool)
    func getFollowingVideos() async throws -> [UIVideo]
    func createAsset(for videoId: Int, quality: VideoQuality) async throws -> AVURLAsset
    func clear()
    func watchEvent(time: Int, video: UIVideo, event: VideoPlayEvent, sessionId: String) async throws
    func getAvailableQualities(for videoId: Int) async throws -> [VideoQuality]
}

protocol LiveServiceProtocol {
    func getAllLives(type: LiveSortType) async throws -> ([UILiveStream], Bool)
    func getNextAllLives(type: LiveSortType) async throws -> ([UILiveStream], Bool)
    func getFollowingLives() async throws -> [UILiveStream]
}

protocol VideoDownloadServiceProtocol {
    func downloadVideo(_ video: UIVideo, quality: VideoQuality) async throws -> URL
    func getDownloadProgress(for videoId: Int) -> VideoDownloadState?
    func cancelDownload(for videoId: Int)
    func getDownloadedVideos() -> [VideoDownloadInfo]
    func deleteDownloadedVideo(for videoId: Int) throws
}

protocol SearchServiceProtocol {
    func getChannels(keyword: String) async throws -> [UIChannel]
    func getLives(keyword: String) async throws -> [UILiveStream]
    func getVideos(keyword: String) async throws -> [UIVideo]
    func getSuggestions(keyword: String) async throws -> [String]
    func getLoungesSuggestions(keyword: String) async throws -> [String]
}

protocol CategoryServiceProtocol {
    func getCategories(usePagination: Bool) async throws -> ([UICategory], Bool)
    func getLivesInCategory(_ category: UICategory, usePagination: Bool) async throws -> ([UILiveStream], Bool)
}

//
//  SearchService.swift
//  ChzzkTV
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 3/30/25.
//

import Foundation

class SearchService: SearchServiceProtocol {
    private let networkService: NetworkService
    
    public init(networkService: NetworkService = NetworkService.shared) {
        self.networkService = networkService
    }
    
    func getChannels(keyword: String) async throws -> [UIChannel] {
        let urlEncodingSearchText = keyword.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) ?? ""
        let searchResponse: SearchResponse = try await networkService.request(
            url: "\(APIEndpoint.searchChannels)?keyword=\(urlEncodingSearchText)")
        return searchResponse.content?.data.map { $0.channel.toUIChannel() } ?? []
    }

    func getLives(keyword: String) async throws -> [UILiveStream] {
        let urlEncodingSearchText = keyword.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) ?? ""
        let searchResponse: SearchResponse = try await networkService.request(
            url: "\(APIEndpoint.searchLives)?keyword=\(urlEncodingSearchText)")
        return searchResponse.content?.data.compactMap { searchResult -> UILiveStream? in
            return searchResult.live?.toUILiveStream(qualities: nil)
        } ?? []
    }

    func getVideos(keyword: String) async throws -> [UIVideo] {
        let urlEncodingSearchText = keyword.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) ?? ""
        let searchResponse: SearchResponse = try await networkService.request(
            url: "\(APIEndpoint.searchVideos)?keyword=\(urlEncodingSearchText)")
        return searchResponse.content?.data.compactMap { searchResult in
            return searchResult.video?.toUIVideo()
        } ?? []
    }
    
    func getSuggestions(keyword: String) async throws -> [String] {
        let urlEncodingSearchText = keyword.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) ?? ""
        let autocompleteResponse: SearchAutocompleResponse = try await networkService.request(
            url: "\(APIEndpoint.searchChannelAutocomplete)?keyword=\(urlEncodingSearchText)")
        return autocompleteResponse.content.data
    }
    
    func getLoungesSuggestions(keyword: String) async throws -> [String] {
        let urlEncodingSearchText = keyword.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) ?? ""
        let autocompleteResponse: SearchAutocompleResponse = try await networkService.request(
            url: "\(APIEndpoint.searchLoungesAutocomplete)?keyword=\(urlEncodingSearchText)")
        return autocompleteResponse.content.data
    }
}

//
//  RecentSearchManager.swift
//  ChzzkTV
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 3/31/25.
//

import Foundation

class RecentSearchManager {
    static let shared = RecentSearchManager()
    
    private let userDefaults = UserDefaults.standard
    private let recentSearchesKey = "RecentSearches"
    private let maxRecentSearches = 10
    
    private init() {}
    
    /// Get recent search terms
    var recentSearches: [String] {
        return userDefaults.stringArray(forKey: recentSearchesKey) ?? []
    }
    
    /// Add a search term to recent searches
    func addRecentSearch(_ searchTerm: String) {
        let trimmedTerm = searchTerm.trimmingCharacters(in: .whitespacesAndNewlines)
        guard !trimmedTerm.isEmpty else { return }
        
        var searches = recentSearches
        
        // Remove if already exists to avoid duplicates
        searches.removeAll { $0.lowercased() == trimmedTerm.lowercased() }
        
        // Add to the beginning
        searches.insert(trimmedTerm, at: 0)
        
        // Keep only the most recent searches
        if searches.count > maxRecentSearches {
            searches = Array(searches.prefix(maxRecentSearches))
        }
        
        userDefaults.set(searches, forKey: recentSearchesKey)
    }
    
    /// Remove a specific search term
    func removeRecentSearch(_ searchTerm: String) {
        var searches = recentSearches
        searches.removeAll { $0.lowercased() == searchTerm.lowercased() }
        userDefaults.set(searches, forKey: recentSearchesKey)
    }
    
    /// Clear all recent searches
    func clearRecentSearches() {
        userDefaults.removeObject(forKey: recentSearchesKey)
    }
}

#if DEBUG && !os(tvOS)
import SwiftUI

#Preview("Recent Search Manager Demo") {
    struct RecentSearchDemo: View {
        @State private var searchText = ""
        @State private var recentSearches: [String] = []
        private let manager = RecentSearchManager.shared

        var body: some View {
            NavigationView {
                VStack(spacing: 20) {
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Add Search Term")
                            .font(.headline)

                        HStack {
                            TextField("Enter search term", text: $searchText)
                                .textFieldStyle(RoundedBorderTextFieldStyle())

                            Button("Add") {
                                if !searchText.isEmpty {
                                    manager.addRecentSearch(searchText)
                                    searchText = ""
                                    loadRecentSearches()
                                }
                            }
                            .disabled(searchText.isEmpty)
                        }
                    }

                    VStack(alignment: .leading, spacing: 8) {
                        HStack {
                            Text("Recent Searches")
                                .font(.headline)

                            Spacer()

                            Button("Clear All") {
                                manager.clearRecentSearches()
                                loadRecentSearches()
                            }
                            .foregroundColor(.red)
                        }

                        if recentSearches.isEmpty {
                            Text("No recent searches")
                                .foregroundColor(.secondary)
                                .italic()
                        } else {
                            ForEach(recentSearches, id: \.self) { search in
                                HStack {
                                    Text(search)
                                    Spacer()
                                    Button("Remove") {
                                        manager.removeRecentSearch(search)
                                        loadRecentSearches()
                                    }
                                    .foregroundColor(.red)
                                    .font(.caption)
                                }
                                .padding(.vertical, 4)
                            }
                        }
                    }

                    Spacer()
                }
                .padding()
                .navigationTitle("Recent Search Manager")
                .onAppear {
                    loadRecentSearches()
                }
            }
        }

        private func loadRecentSearches() {
            recentSearches = manager.recentSearches
        }
    }

    return RecentSearchDemo()
}
#endif

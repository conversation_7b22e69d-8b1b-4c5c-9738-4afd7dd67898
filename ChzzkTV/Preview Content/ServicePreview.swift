//
//  ServicePreview.swift
//  ChzzkTV
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 3/29/25.
//

import SwiftData
import AVKit

class PreviewChannelService: ChannelServiceProtocol {
    let videoCount: Int
    let channelCount: Int
    
    init(videoCount: Int = 5, channelCount: Int = 10) {
        self.videoCount = videoCount
        self.channelCount = channelCount
    }
    
    func toggleFollow(channelId: String) async -> Bool {
        true
    }
    
    func isFollowing(channelId: String) -> Bool {
        true
    }
    
    func getLive(id: String) async throws -> UILiveStream? {
        StreamPreview.sampleUILiveStream
    }
    
    func getLatestVideo(id: String) async throws -> UIVideo? {
        StreamPreview.sampleUIVideo
    }
    
    func getVideos(id: String, page: Int, size: Int) async throws -> ([UIVideo]?, Int?) {
        (StreamPreview.createSampleUIVideos(count: videoCount), 1)
    }
    
    func getChannel(id: String) async throws -> UIChannel {
        StreamPreview.sampleUIChannel
    }
    
    func getLiveWithPlaybackUrl(id: String) async throws -> UILiveStream? {
        StreamPreview.sampleUILiveStream
    }
    
    func getFollowingChannels() async throws -> [UIChannel] {
        StreamPreview.createSampleUIChannels(count: channelCount)
    }
}

class PreviewLiveService: LiveServiceProtocol {
    let liveCount: Int
    
    init(liveCount: Int = 10) {
        self.liveCount = liveCount
    }
    
    func getAllLives(type: LiveSortType) async throws -> ([UILiveStream], Bool) {
        (StreamPreview.createSampleUILiveStreams(count: liveCount), false)
    }
    
    func getNextAllLives(type: LiveSortType) async throws -> ([UILiveStream], Bool) {
        ([], false)
    }
    
    func getFollowingLives() async throws -> [UILiveStream] {
        StreamPreview.createSampleUILiveStreams(count: liveCount)
    }
}

class PreviewVideoService: VideoServiceProtocol {
    let videoCount: Int
    
    init(videoCount: Int = 10) {
        self.videoCount = videoCount
    }
    
    func getAvailableQualities(for videoId: Int) async throws -> [VideoQuality] {
        []
    }
    
    func getVideo(_ videoId: Int) async throws -> (UIVideo, Bool) {
        (StreamPreview.sampleUIVideo, false)
    }
    
    func getFollowingVideos() async throws -> [UIVideo] {
        StreamPreview.createSampleUIVideos(count: videoCount)
    }
    
    func createAsset(for videoId: Int, quality: VideoQuality) async throws -> AVURLAsset {
        AVURLAsset(url: URL(string: "")!)
    }
    
    func clear() {
        
    }
    
    func watchEvent(time: Int, video: UIVideo, event: VideoPlayEvent, sessionId: String) async throws {
        
    }
}

class PreviewSearchService: SearchServiceProtocol {
    func getChannels(keyword: String) async throws -> [UIChannel] {
        StreamPreview.createSampleUIChannels(count: 20)
    }

    func getLives(keyword: String) async throws -> [UILiveStream] {
        StreamPreview.createSampleUILiveStreams(count: 15)
    }

    func getVideos(keyword: String) async throws -> [UIVideo] {
        StreamPreview.createSampleUIVideos(count: 15)
    }

    func getSuggestions(keyword: String) async throws -> [String] {
        ["A", "B", "C"]
    }

    func getLoungesSuggestions(keyword: String) async throws -> [String] {
        ["Lounge A", "Lounge B", "Lounge C"]
    }
}

class PreviewCategoryService: CategoryServiceProtocol {
    func getCategories(usePagination: Bool = false) async throws -> ([UICategory], Bool) {
        if usePagination {
            return ([], false)
        } else {
            return (StreamPreview.createSampleUICategories(count: 20), false)
        }
    }
    
    func getLivesInCategory(_ category: UICategory, usePagination: Bool = false) async throws -> ([UILiveStream], Bool) {
        if usePagination {
            return ([], false)
        } else {
            return (StreamPreview.createSampleUILiveStreams(count: 20), false)
        }
    }
}


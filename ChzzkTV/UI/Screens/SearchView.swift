import SwiftUI

struct SearchView: View {
    @StateObject private var viewModel: SearchViewModel
    @State private var searchText = ""
    @State private var selectedLiveStream: UILiveStream? = nil
    @State private var selectedVideo: UIVideo? = nil

    @Environment(\.channelService) private var channelService
    @Environment(\.videoService) private var videoService

    init(viewModel: SearchViewModel) {
        _viewModel = .init(wrappedValue: viewModel)
    }
    
    var body: some View {
        NavigationStack {
            contentView
                .navigationDestination(for: UIChannel.self) { channel in
                    if let channelId = channel.id {
                        ChannelView(
                            channelId: channelId,
                            viewModel: ChannelViewModel(
                                channelService: channelService
                            )
                        )
                    }
                }
                .toolbar(.hidden, for: .navigationBar)
#if os(tvOS)
                .searchSuggestions {
                    ForEach(viewModel.suggestions, id: \.self) { suggestion in
                        Text(suggestion)
                    }
                }
                .padding(.top)
#endif
        }
        .fullScreenCover(item: $selectedLiveStream) { stream in
            LiveDetailView(stream: stream, viewModel: .init(
                channelService: channelService
            ))
        }
        .fullScreenCover(item: $selectedVideo) { video in
            VideoDetailView(video: video, viewModel: .init(
                videoService: videoService
            ))
        }
    }
    
    private var contentView: some View {
        ScrollView {
#if os(tvOS)
            searchResultsView
#else
            if !viewModel.suggestions.isEmpty {
                suggestionsView
            } else if viewModel.isLoading {
                loadingView
            } else if viewModel.isEmpty {
                recentSearchesView
            } else if viewModel.hasSearchResults {
                searchResultsView
            } else {
                emptyResultsView
            }
#endif
        }
#if !os(tvOS)
        .safeAreaInset(edge: .bottom) {
            Color.clear.frame(height: 60)
        }
#endif
        .searchable(text: $searchText)
        .onSubmit(of: .search) {
            if !searchText.isEmpty {
                Task {
                    await viewModel.search(query: searchText)
                }
            }
        }
        .onChange(of: searchText) { _, newValue in
            if newValue.isEmpty {
                viewModel.clearSearchResults()
                return
            }
            Task {
                await viewModel.loadSuggestions(query: newValue)
#if os(tvOS)
                await viewModel.search(query: newValue)
#endif
            }
        }
        .onAppear {
            viewModel.loadRecentSearches()
        }
    }

    @ViewBuilder
    private var recentSearchesView: some View {
        if !viewModel.recentSearches.isEmpty {
            VStack(alignment: .leading, spacing: 16) {
                HStack {
                    Text("Recent Searches")
                        .font(.title2)
                        .fontWeight(.bold)
                        .padding(.horizontal)

                    Spacer()

                    Button("Clear All") {
                        viewModel.clearRecentSearches()
                    }
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .padding(.horizontal)
                }

                LazyVStack(alignment: .leading, spacing: 8) {
                    ForEach(viewModel.recentSearches, id: \.self) { searchTerm in
                        HStack {
                            Button(action: {
                                searchText = searchTerm
                                Task {
                                    await viewModel.searchFromSelection(query: searchTerm)
                                }
                            }) {
                                HStack {
                                    Image(systemName: "clock.arrow.circlepath")
                                        .foregroundColor(.secondary)
                                    Text(searchTerm)
                                        .foregroundColor(.primary)
                                    Spacer()
                                }
                                .padding(.horizontal)
                                .padding(.vertical, 8)
                            }
                            .buttonStyle(.plain)

                            Button(action: {
                                viewModel.removeRecentSearch(searchTerm)
                            }) {
                                Image(systemName: "xmark.circle.fill")
                                    .foregroundColor(.secondary)
                            }
                            .buttonStyle(.plain)
                            .padding(.trailing)
                        }
                    }
                }
            }
        } else {
            VStack(spacing: 16) {
                Image(systemName: "magnifyingglass")
                    .font(.system(size: 48))
                    .foregroundColor(.secondary)
                Text("Search for channels, lives, and videos")
                    .font(.title3)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
            .padding()
        }
    }

    @ViewBuilder
    private var suggestionsView: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Suggestions")
                .font(.title2)
                .fontWeight(.bold)
                .padding(.horizontal)

            LazyVStack(alignment: .leading, spacing: 8) {
                ForEach(viewModel.suggestions, id: \.self) { suggestion in
                    Button(action: {
                        searchText = suggestion
                        Task {
                            await viewModel.searchFromSelection(query: suggestion)
                        }
                    }) {
                        HStack {
                            Image(systemName: "magnifyingglass")
                                .foregroundColor(.secondary)
                            Text(suggestion)
                                .foregroundColor(.primary)
                            Spacer()
                        }
                        .padding(.horizontal)
                        .padding(.vertical, 8)
                    }
                    .buttonStyle(.plain)
                }
            }
        }
        .padding(.top)
    }

    @ViewBuilder
    private var searchResultsView: some View {
        VStack(alignment: .leading, spacing: 0) {
            // Channels section
            if !viewModel.channels.isEmpty {
                Text("Channels")
                    .font(.title2)
                    .fontWeight(.bold)
                    .padding(.horizontal)

                ChannelHScrollView(
                    channels: viewModel.channels,
                    hideVerifiedIcon: false
                )
            }
            
            // Live streams section
            if !viewModel.lives.isEmpty {
                Text("Live Streams")
                    .font(.title2)
                    .fontWeight(.bold)
                    .padding(.horizontal)

                LiveStreamHScrollView(
                    streams: viewModel.lives,
                    selectedStream: $selectedLiveStream
                )
            }

            // Videos section
            if !viewModel.videos.isEmpty {
                Text("Videos")
                    .font(.title2)
                    .fontWeight(.bold)
                    .padding(.horizontal)

                VideoHScrollView(
                    videos: viewModel.videos,
                    selectedVideo: $selectedVideo
                )
            }
        }
    }

    @ViewBuilder
    private var loadingView: some View {
        VStack(spacing: 16) {
            ProgressView()
                .scaleEffect(1.2)
            Text("Searching...")
                .font(.title3)
                .foregroundColor(.secondary)
        }
        .padding()
    }

    @ViewBuilder
    private var emptyResultsView: some View {
        VStack(spacing: 16) {
            Image(systemName: "magnifyingglass")
                .font(.system(size: 48))
                .foregroundColor(.secondary)
            Text("No results found")
                .font(.title3)
                .foregroundColor(.secondary)
            Text("Try searching with different keywords")
                .font(.body)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .padding()
    }
}

#Preview("Empty State") {
    SearchView(
        viewModel: SearchViewModel(
            searchService: PreviewSearchService()
        )
    )
    .environment(\.channelService, PreviewChannelService())
    .environment(\.videoService, PreviewVideoService())
}

#Preview("Recent Searches") {
    let viewModel = SearchViewModel(searchService: PreviewSearchService())
    // Simulate recent searches
    let recentSearchManager = RecentSearchManager.shared
    recentSearchManager.addRecentSearch("스타크래프트")
    recentSearchManager.addRecentSearch("롤")
    recentSearchManager.addRecentSearch("발로란트")
    recentSearchManager.addRecentSearch("오버워치")

    return SearchView(viewModel: viewModel)
        .environment(\.channelService, PreviewChannelService())
        .environment(\.videoService, PreviewVideoService())
        .onAppear {
            viewModel.loadRecentSearches()
        }
}

#Preview("Suggestions") {
    let viewModel = SearchViewModel(searchService: PreviewSearchService())

    return SearchView(viewModel: viewModel)
        .environment(\.channelService, PreviewChannelService())
        .environment(\.videoService, PreviewVideoService())
        .onAppear {
            // Simulate suggestions while typing
            viewModel.suggestions = ["스타크래프트", "스타크래프트 2", "스타크래프트 리마스터", "스타듀밸리"]
        }
}

#Preview("Search Results") {
    let viewModel = SearchViewModel(searchService: PreviewSearchService())

    return SearchView(viewModel: viewModel)
        .environment(\.channelService, PreviewChannelService())
        .environment(\.videoService, PreviewVideoService())
        .onAppear {
            // Simulate search results
            viewModel.channels = StreamPreview.createSampleUIChannels(count: 8)
            viewModel.lives = StreamPreview.createSampleUILiveStreams(count: 6)
            viewModel.videos = StreamPreview.createSampleUIVideos(count: 10)
            viewModel.currentSearchQuery = "스타크래프트"
        }
}

#Preview("Loading State") {
    let viewModel = SearchViewModel(searchService: PreviewSearchService())

    return SearchView(viewModel: viewModel)
        .environment(\.channelService, PreviewChannelService())
        .environment(\.videoService, PreviewVideoService())
        .onAppear {
            viewModel.isLoading = true
            viewModel.currentSearchQuery = "검색중..."
        }
}

#Preview("No Results") {
    let viewModel = SearchViewModel(searchService: PreviewSearchService())

    return SearchView(viewModel: viewModel)
        .environment(\.channelService, PreviewChannelService())
        .environment(\.videoService, PreviewVideoService())
        .onAppear {
            viewModel.currentSearchQuery = "존재하지않는검색어"
            // Empty arrays simulate no results
            viewModel.channels = []
            viewModel.lives = []
            viewModel.videos = []
        }
}

#Preview("Search Results - Lives Only") {
    let viewModel = SearchViewModel(searchService: PreviewSearchService())

    return SearchView(viewModel: viewModel)
        .environment(\.channelService, PreviewChannelService())
        .environment(\.videoService, PreviewVideoService())
        .onAppear {
            viewModel.lives = StreamPreview.createSampleUILiveStreams(count: 8)
            viewModel.currentSearchQuery = "라이브 스트림"
        }
}

#Preview("Search Results - Videos Only") {
    let viewModel = SearchViewModel(searchService: PreviewSearchService())

    return SearchView(viewModel: viewModel)
        .environment(\.channelService, PreviewChannelService())
        .environment(\.videoService, PreviewVideoService())
        .onAppear {
            viewModel.videos = StreamPreview.createSampleUIVideos(count: 12)
            viewModel.currentSearchQuery = "비디오"
        }
}

#Preview("Search Results - Channels Only") {
    let viewModel = SearchViewModel(searchService: PreviewSearchService())

    return SearchView(viewModel: viewModel)
        .environment(\.channelService, PreviewChannelService())
        .environment(\.videoService, PreviewVideoService())
        .onAppear {
            viewModel.channels = StreamPreview.createSampleUIChannels(count: 15)
            viewModel.currentSearchQuery = "채널"
        }
}

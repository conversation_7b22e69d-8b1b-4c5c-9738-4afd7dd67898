import SwiftUI

struct VideoHScrollView: View {
    let videos: [UIVideo]
    @Binding var selectedVideo: UIVideo?
    var onUnfollowed: ((UIChannel) -> Void)?

    @Environment(\.channelService) private var channelService
    @StateObject private var downloadViewModel = VideoDownloadViewModel()

    @State private var followStatus: [String: Bool] = [:]
    
    var body: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            LazyHStack(spacing: Constants.cardSpacing) {
                ForEach(videos) { video in
                    Button {
                        selectedVideo = video
                    } label: {
                        VideoCard(video: video)
                            .frame(width: Constants.videoCardWidth)
                            .clipShape(.rect(cornerRadius: Constants.cornerRadius))
                    }
#if os(tvOS)
                    .buttonStyle(.card)
#else
                    .buttonStyle(.plain)
#endif
                    .contextMenu {
                        contextMenu(video: video)
                    }
                }
            }
            .padding(.horizontal, Constants.cardPadding)
            .padding(.vertical, Constants.cardVerticalPadding)
        }
        .onAppear {
            // Initialize status dictionaries when view appears
            for video in videos {
                if let channelId = video.channel?.id {
                    followStatus[channelId] = channelService.isFollowing(channelId: channelId)
                }
            }
        }
    }
    
    @ViewBuilder
    func contextMenu(video: UIVideo) -> some View {
        // Download option
        Button(action: {
            downloadViewModel.startDownload(for: video)
        }) {
            Label("Download", systemImage: "arrow.down.circle")
        }

        if let channel = video.channel {
            CardContextMenu(
                channel: channel,
                followStatus: Binding(
                    get: { followStatus[channel.id!] ?? false },
                    set: { followStatus[channel.id!] = $0 }
                ),
                onFollowStatusChanged: { newStatus in
                    if !newStatus {
                        onUnfollowed?(channel)
                    }
                }
            )
            .onAppear {
                if followStatus[channel.id!] == nil {
                    followStatus[channel.id!] = channelService.isFollowing(channelId: channel.id!)
                }
            }
        }
    }
}

#Preview {
    ScrollView {
        VStack(alignment: .leading, spacing: 30) {
            ZStack {
                VStack(alignment: .leading) {
                    Text("Latest Videos")
                        .font(.title2)
                        .fontWeight(.bold)
                        .padding(.horizontal)
                    
                    VideoHScrollView(
                        videos: StreamPreview.createSampleUIVideos(count: 10),
                        selectedVideo: .constant(nil)
                    )
                    .background(.gray)
                }
            }
        }
        .padding(.vertical)
    }
}

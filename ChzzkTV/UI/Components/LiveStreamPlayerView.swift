//
//  LiveStreamPlayerView.swift
//  ChzzkTV
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 3/20/25.
//

import SwiftUI
import AVKit
import SwiftData

struct LiveStreamPlayerView: UIViewControllerRepresentable {
    var player: AVPlayer?
    var channel: UIChannel?
    var availableQualities: [VideoQuality]
    var currentQuality: VideoQuality?
    var onQualityChange: (VideoQuality) -> Void
    @Environment(\.channelService) private var channelService
    @Environment(\.dismiss) private var dismiss
    
    // Add a class-level state tracker to observe from coordinator
    class FollowState: ObservableObject {
        @Published var isFollowing: Bool = false
    }
    
    // Create a single shared state for the view
    private let followState = FollowState()
    
    func makeUIViewController(context: Context) -> AVPlayerViewController {
        let controller = AVPlayerViewController()
        controller.player = player
        controller.allowsPictureInPicturePlayback = true
        
        // Set additional playback options to prevent audio processor warnings
        #if os(tvOS)
        controller.appliesPreferredDisplayCriteriaAutomatically = true
        #endif
        
        #if os(iOS)
        controller.canStartPictureInPictureAutomaticallyFromInline = true
        #endif
        
        // Configure the audio session once at controller creation
        do {
            let audioSession = AVAudioSession.sharedInstance()
            
            // Use default settings that don't require special audio processors
            try audioSession.setCategory(.playback)
            
            // Active the session
            try audioSession.setActive(true)
        } catch {
            print("Audio session configuration failed: \(error)")
        }
        
        // Initialize follow state
        if let channelId = channel?.id {
            print("Initializing follow state for channel: \(channelId)")
            let initialFollowState = channelService.isFollowing(
                channelId: channelId
            )
            followState.isFollowing = initialFollowState
            print("Initial follow state: \(initialFollowState)")
        }
        
        // Hide controller UI when using placeholder player
        if player?.currentItem == nil {
            controller.showsPlaybackControls = false
        }
        
        return controller
    }
    
    func updateUIViewController(_ uiViewController: AVPlayerViewController, context: Context) {
        // Update player if needed
        uiViewController.player = player
        
        // Remove redundant audio session configuration to avoid conflicts
        // We already set this up in makeUIViewController
        
        // Show controls once we have a real player with content
        if player?.currentItem != nil {
            uiViewController.showsPlaybackControls = true
        }
        
        // Clear any existing custom menu items
        #if os(tvOS)
        uiViewController.infoViewActions.removeAll()
        uiViewController.transportBarCustomMenuItems.removeAll()
        #endif
        
        // Add quality selection menu if qualities are available
        #if os(tvOS)
        if !availableQualities.isEmpty {
            let qualityMenu = UIMenu(
                title: String(localized: "Quality"),
                image: UIImage(systemName: "gear"),
                children: availableQualities.map { quality in
                    UIAction(
                        title: quality.displayName,
                        state: quality == currentQuality ? .on : .off
                    ) { _ in
                        onQualityChange(quality)
                    }
                }
            )
            
            uiViewController.transportBarCustomMenuItems.append(qualityMenu)
        }
        #endif
        
        // If there's no channel info, don't add any custom actions
        guard let uiChannel = channel, let channelId = uiChannel.id else { return }
        
        // Ensure follow state is up-to-date
        let currentFollowState = channelService.isFollowing(
            channelId: channelId
        )
        if followState.isFollowing != currentFollowState {
            print("Updating follow state to match database: \(currentFollowState)")
            followState.isFollowing = currentFollowState
        }
        
        // Add "Go to channel" action
        #if os(tvOS)
        let goToChannel = UIAction(
            title: String(localized: "Go to channel"),
            image: UIImage(systemName: "person")
        ) { action in
            // Post notification to show the channel
            NotificationCenter.default.post(
                name: .navigateToChannel,
                object: channelId
            )
            
            // Dismiss this view
            context.coordinator.dismissView()
        }
        uiViewController.infoViewActions.append(goToChannel)
        #endif
        
        // Add follow/unfollow action
        context.coordinator.updateFollowButton(in: uiViewController, withChannel: uiChannel)
    }
    
    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }
    
    // Called when view disappears - handle proper cleanup
    func dismantleUIViewController(_ uiViewController: AVPlayerViewController, coordinator: Coordinator) {
        print("PlayerView: dismantling and releasing resources")
        
        // Perform cleanup
        coordinator.cleanup()
        
        // Log final cleanup
        print("PlayerView: dismantled and resources released")
    }
    
    class Coordinator: NSObject, @unchecked Sendable {
        var parent: LiveStreamPlayerView
        
        init(_ parent: LiveStreamPlayerView) {
            self.parent = parent
        }
        
        deinit {
            print("PlayerView Coordinator deallocated")
        }
        
        func dismissView() {
            // Use SwiftUI's dismiss environment value
            DispatchQueue.main.async {
                self.parent.dismiss()
            }
        }
        
        // Add proper cleanup method to release resources
        func cleanup() {
            // Release player resources
            if let player = parent.player {
                // Remove any observers
                NotificationCenter.default.removeObserver(self)
                
                // Stop and reset player
                player.pause()
                player.replaceCurrentItem(with: nil)
                
                // Reset audio session if needed - be careful not to deactivate if PiP is active
                #if os(iOS)
                // Only deactivate audio session if PiP is not active
                if !AVPictureInPictureController.isPictureInPictureSupported() ||
                   AVAudioSession.sharedInstance().category != .playback {
                    try? AVAudioSession.sharedInstance().setActive(false, options: .notifyOthersOnDeactivation)
                }
                #else
                try? AVAudioSession.sharedInstance().setActive(false, options: .notifyOthersOnDeactivation)
                #endif
            }
            
            print("PlayerView: resources cleaned up")
        }
        
        func updateFollowButton(in viewController: AVPlayerViewController, withChannel channel: UIChannel) {
            print("Updating follow button with channel ID: \(channel.id!)")
            
            // Always check database for current state to ensure accuracy
            let databaseFollowState = parent.channelService.isFollowing(
                channelId: channel.id!
            )
            
            // Update our state if it doesn't match database
            if parent.followState.isFollowing != databaseFollowState {
                print("State mismatch detected - updating from \(parent.followState.isFollowing) to \(databaseFollowState)")
                parent.followState.isFollowing = databaseFollowState
            }
            
            // Get current follow state
            let isFollowing = parent.followState.isFollowing
            print("Using follow state for UI: \(isFollowing)")
            
            // Create action based on current state
            let followTitle = isFollowing ? String(localized: "Unfollow") : String(localized: "Follow")
            let followImage = isFollowing ? UIImage(systemName: "heart.fill") : UIImage(systemName: "heart")
            
            let followAction = UIAction(title: followTitle, image: followImage) { [weak self, channel] _ in
                guard let self = self else { return }
                
                // Toggle the follow state
                Task {
                    let newState = await self.parent.channelService.toggleFollow(channelId: channel.id!)
                    print("Toggled follow state to: \(newState)")
                    
                    // Update our observable state
                    await MainActor.run {
                        self.parent.followState.isFollowing = newState
                    }
                    
                    // Recreate the button with new state
                    await MainActor.run {
                        self.updateFollowButton(in: viewController, withChannel: channel)
                    }
                }
            }
            
            // Apply to UI with a clean slate
            #if os(tvOS)
            viewController.transportBarCustomMenuItems.removeAll()
            viewController.transportBarCustomMenuItems.append(followAction)
            #endif
        }
    }
}

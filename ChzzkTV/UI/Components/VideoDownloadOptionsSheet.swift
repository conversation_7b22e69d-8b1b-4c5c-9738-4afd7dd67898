//
//  VideoDownloadOptionsSheet.swift
//  ChzzkTV
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 8/27/25.
//

import SwiftUI

#if !os(tvOS)
struct VideoDownloadOptionsSheet: View {
    @ObservedObject var downloadViewModel: VideoDownloadViewModel
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        VStack(spacing: 20) {
            if let video = downloadViewModel.selectedVideo {
                // Quality Selection Section
                VStack(alignment: .leading, spacing: 12) {
                    Text("Select Quality")
                        .font(.headline)
                    
                    if downloadViewModel.isLoadingQualities {
                        HStack {
                            ProgressView()
                                .scaleEffect(0.8)
                            Text("Loading available qualities...")
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                            Spacer()
                        }
                        .padding()
                    } else {
                        if downloadViewModel.availableQualities.isEmpty {
                            VStack(spacing: 12) {
                                Image(systemName: "clock.arrow.circlepath")
                                    .font(.largeTitle)
                                    .foregroundColor(.secondary)

                                Text("No Download Options Available")
                                    .font(.headline)
                                    .foregroundColor(.primary)

                                Text("The live stream has ended recently and download options are still being processed. Please check back in a few minutes.")
                                    .font(.subheadline)
                                    .foregroundColor(.secondary)
                                    .multilineTextAlignment(.center)
                                    .padding(.horizontal)
                            }
                            .padding(.vertical, 40)
                        } else {
                            LazyVStack(spacing: 8) {
                                ForEach(downloadViewModel.availableQualities) { quality in
                                    QualitySelectionRow(
                                        quality: quality,
                                        isSelected: downloadViewModel.selectedQuality?.id == quality.id,
                                        onTap: {
                                            downloadViewModel.selectedQuality = quality
                                        },
                                        videoDuration: video.duration
                                    )
                                }
                            }
                        }
                    }
                }
                .padding(.top, 20)
                
                Spacer()
                
                // Download Button
                Button(action: {
                    Task {
                        dismiss()
                        await downloadViewModel.downloadVideo()
                    }
                }) {
                    HStack {
                        Image(systemName: "arrow.down.circle.fill")
                        Text("Download Video")
                    }
                    .font(.headline)
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(
                        downloadViewModel.selectedQuality != nil && !downloadViewModel.isLoadingQualities
                        ? Color.blue
                        : Color.gray
                    )
                    .clipShape(RoundedRectangle(cornerRadius: 12))
                }
                .disabled(downloadViewModel.selectedQuality == nil || downloadViewModel.isLoadingQualities)
            }
        }
        .padding()
        
        .alert("Download Error", isPresented: $downloadViewModel.showingDownloadError) {
            Button("OK") {
                downloadViewModel.dismissError()
            }
        } message: {
            if let error = downloadViewModel.downloadError {
                Text(error.localizedDescription)
            }
        }
    }
}

struct VideoInfoView: View {
    var video: UIVideo
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                CachedAsyncImage(url: video.imageUrl) { image in
                    image
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                } placeholder: {
                    Rectangle()
                        .fill(Color.gray.opacity(0.3))
                }
                .frame(width: 120, height: 68)
                .clipShape(RoundedRectangle(cornerRadius: 8))
                
                VStack(alignment: .leading, spacing: 4) {
                    Text(video.title)
                        .font(.headline)
                        .lineLimit(2)
                    
                    if let channel = video.channel {
                        Text(channel.name)
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    }
                    
                    Text(video.formattedDuration)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .clipShape(RoundedRectangle(cornerRadius: 12))
    }
}

struct QualitySelectionRow: View {
    let quality: VideoQuality
    let isSelected: Bool
    let onTap: () -> Void
    let videoDuration: Int? // Duration in seconds

    var body: some View {
        Button(action: onTap) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(quality.displayName)
                        .font(.headline)
                        .foregroundColor(.primary)

                    if let duration = videoDuration {
                        Text("Size: \(formatEstimatedSize(bandwidth: quality.bandwidth, duration: duration))")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }

                Spacer()

                if isSelected {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.blue)
                        .font(.title2)
                }
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(isSelected ? Color.blue.opacity(0.1) : Color(.systemGray6))
                    .overlay(
                        RoundedRectangle(cornerRadius: 8)
                            .stroke(isSelected ? Color.blue : Color.clear, lineWidth: 2)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }

    private func formatBandwidth(_ bandwidth: Int) -> String {
        let mbps = Double(bandwidth) / 1_000_000
        return String(format: "%.1f Mbps", mbps)
    }

    private func formatEstimatedSize(bandwidth: Int, duration: Int) -> String {
        // Calculate estimated size: (bandwidth in bits/sec * duration in sec) / 8 bits/byte
        let estimatedBytes = Double(bandwidth) * Double(duration) / 8.0

        if estimatedBytes >= 1_000_000_000 {
            return String(format: "~%.1f GB", estimatedBytes / 1_000_000_000)
        } else if estimatedBytes >= 1_000_000 {
            return String(format: "~%.0f MB", estimatedBytes / 1_000_000)
        } else {
            return String(format: "~%.0f KB", estimatedBytes / 1_000)
        }
    }
}

#Preview {
    VideoDownloadOptionsSheet(downloadViewModel: VideoDownloadViewModel())
}
#endif

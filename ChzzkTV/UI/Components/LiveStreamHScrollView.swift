import SwiftUI

struct LiveStreamHScrollView: View {
    let streams: [UILiveStream]
    @Binding var selectedStream: UILiveStream?
    var onUnfollowed: ((UIChannel) -> Void)?
    
    @Environment(\.channelService) private var channelService
    
    @State private var followStatus: [String: Bool] = [:]
    
    var body: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            LazyHStack(spacing: Constants.cardSpacing) {
                ForEach(streams) { stream in
                    Button {
                        if !stream.isAbroad {
                            selectedStream = stream
                        }
                    } label: {
                        LiveStreamCard(stream: stream)
                            .frame(width: Constants.videoCardWidth)
                            .clipShape(.rect(cornerRadius: Constants.cornerRadius))
                    }
#if os(tvOS)
                    .buttonStyle(.card)
#else
                    .buttonStyle(.plain)
#endif
                    .contextMenu {
                        contextMenu(stream: stream)
                    }
                }
            }
            .padding(.horizontal, Constants.cardPadding)
            .padding(.vertical, Constants.cardVerticalPadding)
        }
        .onAppear {
            // Initialize status dictionaries when view appears
            for stream in streams {
                if let channelId = stream.channel?.id {
                    followStatus[channelId] = channelService.isFollowing(channelId: channelId)
                }
            }
        }
    }
    
    @ViewBuilder
    func contextMenu(stream: UILiveStream) -> some View {
        if let channel = stream.channel {
            CardContextMenu(
                channel: channel,
                followStatus: Binding(
                    get: { followStatus[channel.id!] ?? false },
                    set: { followStatus[channel.id!] = $0 }
                ),
                onFollowStatusChanged: { newStatus in
                    if !newStatus {
                        onUnfollowed?(channel)
                    }
                }
            )
            .onAppear {
                if followStatus[channel.id!] == nil {
                    followStatus[channel.id!] = channelService.isFollowing(channelId: channel.id!)
                }
            }
        }
    }
}

#Preview {
    ScrollView {
        VStack(alignment: .leading, spacing: 30) {
            ZStack {
                VStack(alignment: .leading) {
                    Text("Lives")
                        .font(.title2)
                        .fontWeight(.bold)
                        .padding(.horizontal)
                    
                    LiveStreamHScrollView(
                        streams: StreamPreview.createSampleUILiveStreams(count: 10),
                        selectedStream: .constant(nil)
                    )
                    .background(.gray)
                }
            }
        }
        .padding(.vertical)
    }
}

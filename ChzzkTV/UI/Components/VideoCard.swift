import SwiftUI
import SwiftData

struct VideoCard: View {
    let video: UIVideo
    var hideChannelName: Bool = false
    @StateObject private var downloadViewModel = VideoDownloadViewModel()

    var body: some View {
        VStack(spacing: 0) {
            ZStack(alignment: .topLeading) {
                ThumbnailView(thumbnail: video)

                VideoCardOverlay(
                    video: video,
                    downloadViewModel: downloadViewModel
                )
                .padding(Constants.cardPadding)
            }
            
            VideoCardInfo(
                video: video,
                hideChannelName: hideChannelName
            )
            .padding(Constants.cardPadding)
            
            // Progress bar
            if let progress = video.progress, progress > 0 {
                GeometryReader { geometry in
                    Rectangle()
                        .fill(.chzzk)
                        .frame(width: geometry.size.width * progress, height: 3)
                        .frame(maxWidth: .infinity, alignment: .leading)
                }
                .frame(height: 3)
            }
        }
#if os(tvOS)
        .background(.ultraThinMaterial)
#else
        .background(Color(.tertiarySystemGroupedBackground))
#endif
        .clipShape(.rect(cornerRadius: Constants.cornerRadius))
    }
}

struct VideoCardOverlay: View {
    let video: UIVideo
    let downloadViewModel: VideoDownloadViewModel
    
    var body: some View {
        VStack(alignment: .leading) {
            text(video.formattedDuration)
            Spacer()
            HStack {
                text(video.relativeDate)
#if !os(tvOS)
                Spacer()
                VideoDownloadButton(video: video, downloadViewModel: downloadViewModel)
#endif
            }
        }
    }
    
    @ViewBuilder
    func text(_ text: String) -> some View {
        let textComponent = Text(text)
            .font(.caption)
            .bold()
            .fontWeight(.medium)
            .padding(.horizontal, 6)
            .padding(.vertical, 3)
        
        if #available(iOS 26.0, tvOS 26.0, *) {
            textComponent
                .glassEffect()
        } else {
            textComponent
                .foregroundStyle(.white)
                .background(.black)
                .cornerRadius(4)
        }
    }
}

struct VideoCardInfo: View {
    let video: UIVideo
    var hideChannelName: Bool
    
    var body: some View {
        VStack(alignment: .leading, spacing: Constants.cardInfoSpacing) {
            if let category = video.category {
                Text(category)
                    .font(.caption)
                    .foregroundStyle(.secondary)
            }
            
            Text(video.title)
                .bold()
                #if os(tvOS)
                .font(.body)
                #else
                .font(.callout)
                #endif
                .lineLimit(2)
                .multilineTextAlignment(.leading)
                .fixedSize(horizontal: false, vertical: true)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            if let channel = video.channel, !hideChannelName {
                HStack(spacing: 8) {
                    if let url = channel.imageUrl {
                        ChannelImage(url: url)
                    }
                    
                    Text(channel.name)
                        .font(.caption)
                        .lineLimit(1)
                }
            }
        }
        .frame(maxWidth: .infinity, alignment: .leading)
    }
}

#Preview {
    VideoCard(video: StreamPreview.sampleUIVideo)
        .frame(
            width: Constants.videoCardWidth,
            height: Constants.videoCardWidth * 9/16)
        .padding()
}

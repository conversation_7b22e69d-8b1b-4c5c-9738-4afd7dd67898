//
//  ChannelCard.swift
//  ChzzkTV
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 3/31/25.
//

import SwiftUI

struct ChannelCard: View {
    let channel: UIChannel
    let hideVerifiedIcon: Bool
    
    init(channel: UIChannel, hideVerifiedIcon: Bool = false) {
        self.channel = channel
        self.hideVerifiedIcon = hideVerifiedIcon
    }
    
    var body: some View {
        VStack {
            ZStack(alignment: .bottomTrailing) {
                ChannelCardImage(url: channel.imageUrl)
                
                if channel.isVerified ?? false, !hideVerifiedIcon {
                    Image(systemName: "checkmark.seal.fill")
                        .foregroundStyle(.chzzk)
                        .frame(width: 20, height: 20)
                }
            }
            
            Text(channel.name)
                .font(.caption)
                .padding(.top, Constants.cardPadding)
                .lineLimit(2)
            
            if channel.followerCount ?? 0 > 0 {
                Text(channel.followerCountFormatted)
                    .font(.caption2)
                    .foregroundStyle(.secondary)
                    .allowsTightening(true)
                    .lineLimit(1)
                    .minimumScaleFactor(0.5)
                    .padding(.top, 1)
            }
        }
    }
}

#Preview {
    ChannelCard(channel: StreamPreview.sampleUIChannel)
        .frame(width: Constants.channelCardSize)
}

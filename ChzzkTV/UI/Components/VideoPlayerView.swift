import SwiftUI
import AVKit
import SwiftData

struct VideoPlayerView: UIViewControllerRepresentable {
    var player: AVPlayer?
    var channel: UIChannel?
    var availableQualities: [VideoQuality]
    var currentQuality: VideoQuality?
    var onQualityChange: (VideoQuality) -> Void
    var onSeekDetected: (() -> Void)?
    
    @Environment(\.dismiss) private var dismiss
    @Environment(\.channelService) private var channelService
    
    class FollowState: ObservableObject {
        @Published var isFollowing: Bool = false
    }
    
    private let followState = FollowState()
    
    func makeUIViewController(context: Context) -> AVPlayerViewController {
        let controller = AVPlayerViewController()
        if let player = player {
            controller.player = player
        }
        
        if let channelId = channel?.id {
            let initialFollowState = channelService.isFollowing(channelId: channelId)
            followState.isFollowing = initialFollowState
        }
        
        // Enable Picture in Picture
        controller.allowsPictureInPicturePlayback = true
        
        #if os(iOS)
        controller.canStartPictureInPictureAutomaticallyFromInline = true
        #endif
        
        // Configure the audio session for PiP support
        do {
            let audioSession = AVAudioSession.sharedInstance()
            try audioSession.setCategory(.playback)
            try audioSession.setActive(true, options: .notifyOthersOnDeactivation)
        } catch {
            print("Failed to configure audio session: \(error)")
        }
        
        // Configure default playback speeds
        controller.speeds = [
            AVPlaybackSpeed(rate: 0.5, localizedName: "0.5x"),
            AVPlaybackSpeed(rate: 1.0, localizedName: "1.0x"),
            AVPlaybackSpeed(rate: 1.25, localizedName: "1.25x"),
            AVPlaybackSpeed(rate: 1.5, localizedName: "1.5x"),
            AVPlaybackSpeed(rate: 1.75, localizedName: "1.75x"),
            AVPlaybackSpeed(rate: 2.0, localizedName: "2.0x")
        ]
        
        return controller
    }
    
    func updateUIViewController(_ uiViewController: AVPlayerViewController, context: Context) {
        uiViewController.player = player

        // Re-setup observation when player changes
        context.coordinator.setupTimeControlStatusObservation()
        
        #if os(tvOS)
        uiViewController.infoViewActions.removeAll()
        uiViewController.transportBarCustomMenuItems.removeAll()
        #endif
        
        // Add quality selection menu
        #if os(tvOS)
        if !availableQualities.isEmpty {
            let qualityMenu = UIMenu(
                title: String(localized: "Quality"),
                image: UIImage(systemName: "gear"),
                children: availableQualities.map { quality in
                    UIAction(
                        title: quality.displayName,
                        state: quality == currentQuality ? .on : .off
                    ) { _ in
                        onQualityChange(quality)
                    }
                }
            )
            
            uiViewController.transportBarCustomMenuItems.append(qualityMenu)
        }
        #endif
        
        guard let channel = channel, let channelId = channel.id else { return }
        
        // Ensure follow state is up-to-date before updating UI
        let currentFollowState = channelService.isFollowing(
            channelId: channelId
        )
        if followState.isFollowing != currentFollowState {
            print("Updating follow state to match current database state: \(currentFollowState)")
            followState.isFollowing = currentFollowState
        }
        
        #if os(tvOS)
        let goToChannel = UIAction(
            title: String(localized: "Go to channel"),
            image: UIImage(systemName: "person")
        ) { action in
            // Post notification to show the channel view after dismissal
            NotificationCenter.default.post(
                name: .navigateToChannel,
                object: channelId
            )
            
            // Dismiss the SwiftUI view
            context.coordinator.dismissView()
        }
        #endif
        
        // Create follow button based on current state
        #if os(tvOS)
        uiViewController.infoViewActions.append(goToChannel)
        #endif
        context.coordinator.updateFollowButton(in: uiViewController, withChannel: channel)
    }
    
    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }
    
    // Called when view disappears - handle proper cleanup
    func dismantleUIViewController(_ uiViewController: AVPlayerViewController, coordinator: Coordinator) {
        print("PlayerView: dismantling and releasing resources")
        
        // Perform cleanup
        coordinator.cleanup()
        
        // Log final cleanup
        print("PlayerView: dismantled and resources released")
    }
    
    class Coordinator: NSObject, @unchecked Sendable {
        var parent: VideoPlayerView
        private var timeControlStatusObservation: NSKeyValueObservation?
        private var wasPlayingBeforePause = false

        init(_ parent: VideoPlayerView) {
            self.parent = parent
            super.init()
            setupTimeControlStatusObservation()
        }
        
        func dismissView() {
            // Use SwiftUI's dismiss environment value
            DispatchQueue.main.async {
                self.parent.dismiss()
            }
        }

        func setupTimeControlStatusObservation() {
            // Clean up existing observation
            timeControlStatusObservation?.invalidate()
            timeControlStatusObservation = nil

            guard let player = parent.player else { return }

            timeControlStatusObservation = player.observe(\.timeControlStatus, options: [.old, .new]) { [weak self] player, change in
                guard let self = self else { return }

                switch player.timeControlStatus {
                case .paused:
                    print("Player paused (maybe scrubbing/seek in progress)")
                    self.wasPlayingBeforePause = true

                case .waitingToPlayAtSpecifiedRate:
                    print("Player waiting (likely after seek)")
                    if self.wasPlayingBeforePause {
                        // This indicates a seek operation completed
                        DispatchQueue.main.async {
                            self.parent.onSeekDetected?()
                        }
                    }

                case .playing:
                    print("Player playing")
                    self.wasPlayingBeforePause = false

                @unknown default:
                    break
                }
            }
        }
        
        // Add proper cleanup method to release resources
        func cleanup() {
            // Clean up time control status observation
            timeControlStatusObservation?.invalidate()
            timeControlStatusObservation = nil

            // Release player resources
            if let player = parent.player {
                // Remove any observers
                NotificationCenter.default.removeObserver(self)
                
                // Stop and reset player
                player.pause()
                player.replaceCurrentItem(with: nil)
                
                // Reset audio session if needed - be careful not to deactivate if PiP is active
                #if os(iOS)
                // Only deactivate audio session if PiP is not active
                if !AVPictureInPictureController.isPictureInPictureSupported() || 
                   AVAudioSession.sharedInstance().category != .playback {
                    try? AVAudioSession.sharedInstance().setActive(false, options: .notifyOthersOnDeactivation)
                }
                #else
                try? AVAudioSession.sharedInstance().setActive(false, options: .notifyOthersOnDeactivation)
                #endif
            }
            
            // Clear any references
            parent.followState.isFollowing = false
            
            print("PlayerView: resources cleaned up")
        }
        
        func updateFollowButton(in viewController: AVPlayerViewController, withChannel channel: UIChannel) {
            print("Updating follow button with channel ID: \(channel.id!)")
            
            // Always check database for current state to ensure accuracy
            let databaseFollowState = parent.channelService.isFollowing(
                channelId: channel.id!
            )
            
            // Update our state if it doesn't match database
            if parent.followState.isFollowing != databaseFollowState {
                print("State mismatch detected - updating from \(parent.followState.isFollowing) to \(databaseFollowState)")
                parent.followState.isFollowing = databaseFollowState
            }
            
            // Get current follow state
            let isFollowing = parent.followState.isFollowing
            print("Using follow state for UI: \(isFollowing)")
            
            // Create action based on current state
            let followTitle = isFollowing ? String(localized: "Unfollow") : String(localized: "Follow")
            let followImage = isFollowing ? UIImage(systemName: "heart.fill") : UIImage(systemName: "heart")
            
            let followAction = UIAction(title: followTitle, image: followImage) { [weak self, channel] _ in
                guard let self = self else { return }
                
                // Toggle the follow state
                Task {
                    let newState = await self.parent.channelService.toggleFollow(channelId: channel.id!)
                    print("Toggled follow state to: \(newState)")
                    
                    // Update our observable state on main actor
                    await MainActor.run {
                        self.parent.followState.isFollowing = newState
                    }
                    
                    // Recreate the button with new state
                    await MainActor.run {
                        self.updateFollowButton(in: viewController, withChannel: channel)
                    }
                }
            }
            
            #if os(tvOS)
            viewController.infoViewActions.append(followAction)
            #endif
        }
    }
}

#Preview {
    VideoPlayerView(player: nil, channel: nil, availableQualities: [], currentQuality: nil, onQualityChange: { _ in }, onSeekDetected: nil)
}

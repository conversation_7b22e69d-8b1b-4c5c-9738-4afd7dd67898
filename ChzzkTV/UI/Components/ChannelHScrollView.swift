//
//  ChannelHScrollView.swift
//  ChzzkTV
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 3/31/25.
//

import SwiftUI

struct ChannelHScrollView: View {
    let channels: [UIChannel]
    var onUnfollowed: ((UIChannel) -> Void)?
    var hideVerifiedIcon: Bool = true
    
    @Environment(\.channelService) private var channelService
    
    @State private var followStatus: [String: Bool] = [:]
    
    var body: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            LazyHStack(spacing: Constants.cardSpacing) {
                ForEach(channels) { channel in
                    NavigationLink(value: channel) {
                        ChannelCard(channel: channel, hideVerifiedIcon: hideVerifiedIcon)
                            .frame(width: Constants.channelCardSize)
                    }
                    .buttonBorderShape(.circle)
                    .contextMenu {
                        contextMenu(channel: channel)
                    }
                }
            }
            #if os(tvOS)
            .buttonStyle(.borderless)
            #else
            .buttonStyle(.plain)
            #endif
            .padding(.horizontal, Constants.cardPadding)
            .padding(.vertical, Constants.cardVerticalPadding)
        }
        .onAppear {
            // Initialize status dictionaries when view appears
            for case let channelId? in channels.map(\.id) {
                followStatus[channelId] = channelService.isFollowing(channelId: channelId)
            }
        }
    }
    
    @ViewBuilder
    func contextMenu(channel: UIChannel) -> some View {
        CardContextMenu(
            channel: channel,
            followStatus: Binding(
                get: { followStatus[channel.id!] ?? false },
                set: { followStatus[channel.id!] = $0 }
            ),
            onFollowStatusChanged: { newStatus in
                if !newStatus {
                    onUnfollowed?(channel)
                }
            }
        )
        .onAppear {
            if followStatus[channel.id!] == nil {
                followStatus[channel.id!] = channelService.isFollowing(channelId: channel.id!)
            }
        }
    }
}

#Preview {
    ScrollView {
        VStack(alignment: .leading, spacing: 30) {
            ZStack {
                VStack(alignment: .leading) {
                    Text("Following Channels")
                        .font(.title2)
                        .fontWeight(.bold)
                        .padding(.horizontal)
                    
                    ChannelHScrollView(
                        channels: StreamPreview.createSampleUIChannels(count: 10)
                    )
                    .background(.gray)
                }
            }
        }
        .padding(.vertical)
    }
}

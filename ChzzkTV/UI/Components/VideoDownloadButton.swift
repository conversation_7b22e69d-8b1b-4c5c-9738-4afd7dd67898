//
//  VideoDownloadButton.swift
//  ChzzkTV
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 8/27/25.
//

import SwiftUI

#if !os(tvOS)
struct DownloadButtonModifier: ViewModifier {
    func body(content: Content) -> some View {
        if #available(iOS 26.0, *) {
            content
                .padding(6)
                .glassEffect()
        } else {
            content
                .foregroundStyle(.white)
                .padding(6)
                .background(.black.opacity(0.7), in: Capsule())
        }
    }
}

struct VideoDownloadButton: View {
    let video: UIVideo
    @ObservedObject var downloadViewModel: VideoDownloadViewModel
    @ObservedObject private var downloadService = VideoDownloadService.shared
    @State private var showingDownloadOptions = false
    @State private var showingCancelAlert = false

    private var downloadedFileURL: URL? {
        // Get the completed download info for this video
        if let downloadInfo = downloadService.completedDownloads[video.id] {
            return downloadService.getDownloadedFileURL(for: downloadInfo)
        }
        return nil
    }
    
    var body: some View {
        Button(action: {
            handleDownloadAction()
        }) {
            downloadButtonContent
        }
        .buttonStyle(PlainButtonStyle())
        .sheet(isPresented: $showingDownloadOptions) {
            VideoDownloadOptionsSheet(downloadViewModel: downloadViewModel)
                .presentationDetents([.medium])
        }
        .alert("Cancel Download", isPresented: $showingCancelAlert) {
            Button("Cancel Download", role: .destructive) {
                downloadService.cancelDownload(for: video.id)
            }
            Button("Continue Download", role: .cancel) { }
        } message: {
            Text("Are you sure you want to cancel downloading '\(video.title)'?")
        }
    }
    
    @ViewBuilder
    private var downloadButtonContent: some View {
        let downloadState = downloadService.getDownloadProgress(for: video.id)
        
        switch downloadState {
        case .none, .cancelled, .failed:
            Image(systemName: "arrow.down.circle")
                .modifier(DownloadButtonModifier())

        case .pending:
            Image(systemName: "clock.circle")
                .foregroundColor(.orange)
                .modifier(DownloadButtonModifier())

        case .downloading(let progress):
            CircularProgressView(progress: progress, size: 20)
                .modifier(DownloadButtonModifier())

        case .processing:
            ProgressView()
                .scaleEffect(1.0)
                .modifier(DownloadButtonModifier())

        case .completed:
            if let fileURL = downloadedFileURL {
                ShareLink(item: fileURL) {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.green)
                        .modifier(DownloadButtonModifier())
                }
            } else {
                Image(systemName: "checkmark.circle.fill")
                    .foregroundColor(.green)
                    .modifier(DownloadButtonModifier())
                    .onTapGesture {
                        VideoDownloadService.shared.openFilesApp()
                    }
            }
        }
    }
    
    private func handleDownloadAction() {
        let downloadState = downloadService.getDownloadProgress(for: video.id)
        
        switch downloadState {
        case .none, .cancelled, .failed:
            downloadViewModel.startDownload(for: video)
            showingDownloadOptions = true
            
        case .pending, .downloading, .processing:
            showingCancelAlert = true
            
        case .completed:
            // ShareLink will handle the sharing
            // No action needed here as ShareLink handles the tap
            break
        }
    }
}

#Preview {
    VStack(spacing: 20) {
        VideoDownloadButton(
            video: StreamPreview.sampleUIVideo,
            downloadViewModel: VideoDownloadViewModel()
        )
    }
    .padding()
}
#endif

//
//  VideoViewModel.swift
//  ChzzkTV
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 3/25/25.
//

import AVKit
import MediaPlayer
import SwiftData

enum VideoPlaybackError: Error {
    case missingVideoData(String)
    case invalidM3U8URL
    case invalidPlaylist
    case serverError
}

enum VideoDetailError: Error {
    case videoIdNotFound
    case unknownPlaybackResponse
    case invalidUrl
    case networkError(String)
    case parserError(String)
    case invalidGdaParameter
}

@MainActor
class VideoViewModel: ObservableObject {
    // MARK: - Properties
    @Published private(set) var player: AVPlayer?
    @Published var isLoading = false
    @Published var error: Error?
    @Published var video: UIVideo?
    @Published private(set) var availableQualities: [VideoQuality] = []
    @Published private(set) var currentQuality: VideoQuality?

    private var playerItem: AVPlayerItem?
    private var timeObserver: Any?
    private var videoService: VideoServiceProtocol
    private var sessionId: String = ""
    private var hasStartedWatching: Bool = false
    private var isLoadingVideo: Bool = false
    private var observerId: String = ""
    private var lastWatchEventTime: Int = -1
    private var isSendingWatchEvent: Bool = false
    
    init(videoService: VideoServiceProtocol) {
        self.videoService = videoService
    }
    
    // MARK: - Public Methods
    func loadVideo(_ videoId: Int) async {
        // Prevent multiple simultaneous loads
        guard !isLoadingVideo else {
            return
        }

        isLoadingVideo = true
        isLoading = true
        error = nil

        // Clean up any existing player/observers first
        cleanUpPlayer()

        // Reset session state for new video
        sessionId = UUID().uuidString
        hasStartedWatching = false
        lastWatchEventTime = -1
        isSendingWatchEvent = false

        do {
            (video, _) = try await videoService.getVideo(videoId)
            availableQualities = try await videoService.getAvailableQualities(for: videoId)
            currentQuality = availableQualities.first

            if let quality = currentQuality {
                let asset = try await videoService.createAsset(for: videoId, quality: quality)
                let imageData = await video?.channel?.imageData()
                setupPlayer(with: asset, imageData: imageData)
            }
        } catch {
            self.error = error
            print("error: \(error)")
        }

        isLoading = false
        isLoadingVideo = false
    }
    
    func changeQuality(_ quality: VideoQuality) async {
        guard let videoId = video?.id else { return }

        isLoading = true
        error = nil

        do {
            // Store current playback position
            let currentTime = player?.currentTime()

            // Clean up existing player (but keep session state)
            cleanUpPlayer()

            let asset = try await videoService.createAsset(for: videoId, quality: quality)
            let imageData = await video?.channel?.imageData()

            // Setup new player with selected quality
            setupPlayer(with: asset, imageData: imageData)

            // Restore playback position
            if let time = currentTime {
                await player?.seek(to: time)
            }

            currentQuality = quality
        } catch {
            self.error = error
            print("error: \(error)")
        }

        isLoading = false
    }
    
    func prepareForDismissal() {
        cleanUp()
    }
    
    func resetError() {
        error = nil
    }
    
    func togglePlayback() {
        guard let player = player else { return }
        
        if player.rate > 0 {
            player.pause()
        } else {
            player.play()
        }
        updateNowPlayingInfo()
    }
    
    // MARK: - Private Methods
    private func setupPlayer(with asset: AVAsset, imageData: Data?) {
        // Ensure we have a clean state - this should already be done but double-check
        if timeObserver != nil {
            removeTimeObserver()
        }

        self.playerItem = AVPlayerItem(asset: asset)
        self.playerItem?.externalMetadata = createMetadataItems(for: video, imageData: imageData)
        self.player = AVPlayer(playerItem: playerItem)
        self.player?.audiovisualBackgroundPlaybackPolicy = .continuesIfPossible

        // Restore last playback position if available
        if let progress = video?.progress,
           let duration = video?.duration {
            let lastPosition = progress * Double(duration)
            let time = CMTime(seconds: lastPosition, preferredTimescale: 1)
            player?.seek(to: time)
        }

        // Add periodic time observer - only one per session
        setupWatchEventObserver()

        self.player?.play()
    }

    private func removeTimeObserver() {
        if let observer = timeObserver {
            player?.removeTimeObserver(observer)
            timeObserver = nil
            observerId = ""
        }
    }

    private func setupWatchEventObserver() {
        guard timeObserver == nil else {
            return
        }

        guard let player = player else {
            return
        }

        observerId = String(UUID().uuidString.prefix(8))
        let interval = CMTime(seconds: 10.0, preferredTimescale: CMTimeScale(NSEC_PER_SEC))
        timeObserver = player.addPeriodicTimeObserver(forInterval: interval, queue: .main) { [weak self] time in
            Task { @MainActor in
                await self?.sendWatchEvent(at: Int(time.seconds))
            }
        }
    }

    func handleSeekDetected() async {
        guard let video = self.video, let player = self.player else { return }

        let currentTime = Int(CMTimeGetSeconds(player.currentTime()))

        do {
            try await videoService.watchEvent(
                time: currentTime,
                video: video,
                event: .resumed,
                sessionId: sessionId
            )
            print("Sent seek resumed event at \(currentTime) seconds")
        } catch {
            print("Failed to send seek resumed event: \(error)")
        }
    }

    private func sendWatchEvent(at timeSeconds: Int) async {
        guard let video = self.video else { return }

        // Prevent duplicate calls for the same time or if already sending
        if timeSeconds == lastWatchEventTime || isSendingWatchEvent {
            return
        }

        isSendingWatchEvent = true
        lastWatchEventTime = timeSeconds

        let eventType: VideoPlayEvent = hasStartedWatching ? .continued : .started

        // Mark as started immediately to prevent race conditions
        if !hasStartedWatching {
            hasStartedWatching = true
        }

        do {
            try await videoService.watchEvent(
                time: timeSeconds,
                video: video,
                event: eventType,
                sessionId: sessionId
            )
        } catch {
            print("Failed to send watch event: \(error)")
            // If this was the first event and it failed, reset the flag
            if eventType == .started {
                hasStartedWatching = false
            }
        }

        isSendingWatchEvent = false
    }
    
    private func createMetadataItems(
        for video: UIVideo?,
        imageData: Data?
    ) -> [AVMetadataItem] {
        var mapping: [AVMetadataIdentifier: Any] = [
            .commonIdentifierTitle: video?.title ?? "Unknown",
            .iTunesMetadataTrackSubTitle: video?.channel?.name ?? "",
            .commonIdentifierDescription:
"""
\(video?.category ?? "")
\(video?.readableDate ?? "")
\(video?.channel?.followerCountFormatted ?? "")
"""
        ]
        if let imageData = imageData {
            mapping[.commonIdentifierArtwork] = imageData
        }
        
        return mapping.compactMap { createMetadataItem(for: $0, value: $1) }
    }
    
    private func createMetadataItem(for identifier: AVMetadataIdentifier,
                                    value: Any) -> AVMetadataItem {
        let item = AVMutableMetadataItem()
        item.identifier = identifier
        item.value = value as? NSCopying & NSObjectProtocol
        // Specify "und" to indicate an undefined language.
        item.extendedLanguageTag = "und"
        return item.copy() as! AVMetadataItem
    }
    
    private func cleanUp() {
        if var video = self.video,
           let player = self.player {
            let seconds = CMTimeGetSeconds(player.currentTime())
            let time = Int(floor(seconds))
            video.progress = Double(time / video.duration)
            Task {
                try await videoService.watchEvent(
                    time: time,
                    video: video,
                    event: .ended,
                    sessionId: sessionId
                )
            }
        }

        cleanUpPlayer()

        // Reset session state
        sessionId = ""
        hasStartedWatching = false

        // Reset audio session
        try? AVAudioSession.sharedInstance().setActive(false, options: .notifyOthersOnDeactivation)
    }

    private func cleanUpPlayer() {
        // Remove time observer
        removeTimeObserver()

        // Stop and reset player
        player?.pause()
        player?.replaceCurrentItem(with: nil)
        player = nil

        // Clear player item
        playerItem = nil
    }
    
    private func deactivateAudioSession() {
        do {
            try AVAudioSession.sharedInstance().setActive(false, options: .notifyOthersOnDeactivation)
        } catch {
            print("Failed to deactivate audio session: \(error)")
        }
    }
    
    private func updateNowPlayingInfo() {
        var nowPlayingInfo = [String: Any]()
        
        nowPlayingInfo[MPMediaItemPropertyTitle] = video?.title ?? "Unknown"
        nowPlayingInfo[MPMediaItemPropertyArtist] = video?.channel?.name ?? ""
        
        if let duration = video?.duration {
            nowPlayingInfo[MPMediaItemPropertyPlaybackDuration] = Double(duration)
        }
        
        nowPlayingInfo[MPNowPlayingInfoPropertyPlaybackRate] = player?.rate ?? 1.0
        
        MPNowPlayingInfoCenter.default().nowPlayingInfo = nowPlayingInfo
    }
}

import Foundation

@MainActor
class SearchViewModel: ObservableObject {
    @Published var channels: [UIChannel] = []
    @Published var lives: [UILiveStream] = []
    @Published var videos: [UIVideo] = []
    @Published var suggestions: [String] = []
    @Published var recentSearches: [String] = []
    @Published var isLoading = false
    @Published var error: Error?
    @Published var currentSearchQuery: String = ""
    @Published var shouldSkipSuggestions = false

    private let searchService: SearchServiceProtocol
    private let recentSearchManager = RecentSearchManager.shared

    var hasSearchResults: Bool {
        !channels.isEmpty || !lives.isEmpty || !videos.isEmpty
    }

    var isEmpty: Bool {
        channels.isEmpty && lives.isEmpty && videos.isEmpty
    }

    init(searchService: SearchServiceProtocol) {
        self.searchService = searchService
        loadRecentSearches()
    }
        
    func search(query: String) async {
        guard !query.isEmpty else {
            clearSearchResults()
            return
        }

        currentSearchQuery = query
#if !os(tvOS)
        suggestions = []
#endif

        isLoading = true
        error = nil

        // Add to recent searches
        recentSearchManager.addRecentSearch(query)
        loadRecentSearches()

        async let channelTask: () = searchChannels(query: query)
        async let livesTask: () = searchLives(query: query)
        async let videosTask: () = searchVideos(query: query)

        await _ = [channelTask, livesTask, videosTask]

        isLoading = false
    }

    func searchFromSelection(query: String) async {
        shouldSkipSuggestions = true
        await search(query: query)
        shouldSkipSuggestions = false
    }

    func loadSuggestions(query: String) async {
        guard !query.isEmpty else {
            suggestions = []
            return
        }

        // Skip loading suggestions if we're performing a search from selection
        guard !shouldSkipSuggestions else {
            return
        }

        // Fetch both suggestions concurrently
        async let channelSuggestionsTask = searchService.getSuggestions(keyword: query)
        async let loungeSuggestionsTask = searchService.getLoungesSuggestions(keyword: query)

        do {
            let channelSuggestions = try await channelSuggestionsTask
            let loungeSuggestions = try await loungeSuggestionsTask

            // Combine suggestions maintaining order: channel first, then lounge
            var combinedSuggestions = channelSuggestions
            for loungeSuggestion in loungeSuggestions {
                if !combinedSuggestions.contains(loungeSuggestion) {
                    combinedSuggestions.append(loungeSuggestion)
                }
            }
            suggestions = combinedSuggestions
        } catch {
            self.error = error
        }
    }

    func clearSearchResults() {
        channels = []
        lives = []
        videos = []
        suggestions = []
        currentSearchQuery = ""
    }

    func loadRecentSearches() {
        recentSearches = recentSearchManager.recentSearches
    }

    func removeRecentSearch(_ searchTerm: String) {
        recentSearchManager.removeRecentSearch(searchTerm)
        loadRecentSearches()
    }

    func clearRecentSearches() {
        recentSearchManager.clearRecentSearches()
        loadRecentSearches()
    }

    private func searchChannels(query: String) async {
        do {
            channels = try await searchService.getChannels(keyword: query)
        } catch {
            self.error = error
        }
    }

    private func searchLives(query: String) async {
        do {
            lives = try await searchService.getLives(keyword: query)
        } catch {
            self.error = error
        }
    }

    private func searchVideos(query: String) async {
        do {
            videos = try await searchService.getVideos(keyword: query)
        } catch {
            self.error = error
        }
    }
}

#if DEBUG && !os(tvOS)
import SwiftUI

#Preview("SearchViewModel States") {
    struct SearchViewModelDemo: View {
        @StateObject private var viewModel = SearchViewModel(
            searchService: PreviewSearchService()
        )
        @State private var searchText = ""

        var body: some View {
            NavigationView {
                VStack(spacing: 20) {
                    // Search input
                    HStack {
                        TextField("Search...", text: $searchText)
                            .textFieldStyle(RoundedBorderTextFieldStyle())

                        Button("Search") {
                            Task {
                                await viewModel.search(query: searchText)
                            }
                        }
                        .disabled(searchText.isEmpty)

                        Button("Suggestions") {
                            Task {
                                await viewModel.loadSuggestions(query: searchText)
                            }
                        }
                        .disabled(searchText.isEmpty)
                    }

                    // State indicators
                    HStack {
                        VStack(alignment: .leading) {
                            Text("State:")
                                .font(.caption)
                                .foregroundColor(.secondary)

                            if viewModel.isLoading {
                                Text("Loading...")
                                    .foregroundColor(.blue)
                            } else if viewModel.isEmpty {
                                Text("Empty")
                                    .foregroundColor(.gray)
                            } else if viewModel.hasSearchResults {
                                Text("Has Results")
                                    .foregroundColor(.green)
                            } else {
                                Text("No Results")
                                    .foregroundColor(.orange)
                            }
                        }

                        Spacer()

                        VStack(alignment: .trailing) {
                            Text("Query:")
                                .font(.caption)
                                .foregroundColor(.secondary)
                            Text(viewModel.currentSearchQuery.isEmpty ? "None" : viewModel.currentSearchQuery)
                                .font(.caption)
                        }
                    }
                    .padding()
                    .background(Color.gray.opacity(0.1))
                    .cornerRadius(8)

                    // Results summary
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Results Summary")
                            .font(.headline)

                        HStack {
                            Label("\(viewModel.channels.count)", systemImage: "person.circle")
                            Spacer()
                            Label("\(viewModel.lives.count)", systemImage: "dot.radiowaves.left.and.right")
                            Spacer()
                            Label("\(viewModel.videos.count)", systemImage: "play.rectangle")
                        }
                        .font(.caption)
                    }
                    .padding()
                    .background(Color.blue.opacity(0.1))
                    .cornerRadius(8)

                    // Suggestions
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Suggestions")
                            .font(.headline)

                        if viewModel.suggestions.isEmpty {
                            Text("No suggestions")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        } else {
                            ForEach(viewModel.suggestions, id: \.self) { suggestion in
                                Button(suggestion) {
                                    searchText = suggestion
                                    Task {
                                        await viewModel.search(query: suggestion)
                                    }
                                }
                                .font(.caption)
                            }
                        }
                    }
                    .padding()
                    .background(Color.orange.opacity(0.1))
                    .cornerRadius(8)

                    // Recent searches
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Recent Searches")
                            .font(.headline)

                        if viewModel.recentSearches.isEmpty {
                            Text("No recent searches")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        } else {
                            ForEach(viewModel.recentSearches, id: \.self) { search in
                                Button(search) {
                                    searchText = search
                                    Task {
                                        await viewModel.search(query: search)
                                    }
                                }
                                .font(.caption)
                            }
                        }
                    }
                    .padding()
                    .background(Color.green.opacity(0.1))
                    .cornerRadius(8)

                    Spacer()
                }
                .padding()
                .navigationTitle("SearchViewModel Demo")
                .onAppear {
                    viewModel.loadRecentSearches()
                }
            }
        }
    }

    return SearchViewModelDemo()
}
#endif
